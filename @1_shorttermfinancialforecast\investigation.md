# Financial Forecast Investigation

## Phase 1: Forecast Pipeline Flexibility

### Model Modifications
1. Added configurable `forecast_start_date` parameter to `prep.billing_unified_forecast`
   - Defaults to current_date() if not specified
   - Allows backtesting by setting historical dates

2. Modified historical data filtering
   - Changed from fixed 12-month lookback to dynamic filtering based on forecast_start_date
   - All data before forecast_start_date is now considered historical
   - This enables point-in-time analysis for backtesting

3. Updated eligibility criteria
   - All date-based criteria now use forecast_start_date instead of current_date
   - Maintains consistency in eligibility determination for historical forecasts
   - Preserves existing business logic while enabling backtesting

### Next Steps for Testing
1. Need to validate the changes with different forecast_start_date values:
   ```sql
   -- Test with current date (default behavior)
   dbt run --select prep.billing_unified_forecast
   
   -- Test with historical date (e.g., 3 months ago)
   dbt run --select prep.billing_unified_forecast --vars '{"forecast_start_date": "2023-12-01"}'
   ```

2. Key metrics to validate:
   - Number of eligible placements
   - Distribution of forecast confidence scores
   - Historical data points used for each placement
   - Forecast end dates

3. Potential areas to investigate:
   - Impact on forecast accuracy at different time horizons
   - Effect of historical data availability on eligibility
   - Distribution of forecast confidence scores
   - Seasonal patterns in forecast accuracy

### Questions to Address
1. How does forecast accuracy vary with different forecast_start_date values?
2. Are there specific time periods where the model performs better/worse?
3. What is the optimal amount of historical data needed for accurate forecasts?
4. How do different business segments perform in terms of forecast accuracy?

## Data Quality Checks
To be added after initial testing:
- Outlier analysis
- Data completeness metrics
- Historical pattern analysis
- Forecast accuracy metrics

version: '3'

services:
    # Python with Jupyter
    dev:
        build:
            context: ..
            dockerfile: .devcontainer/dev.Dockerfile
            args:
                VARIANT: "3.9"
                NODE_VERSION: "none"
        env_file:
            - ../.env
        ports:
            - 8180:8080
            - 5080:80
        volumes:
            - ..:/workspace:cached
        # Overrides default command so things don't shut down after the process ends.
        command: sleep infinity
        extra_hosts:
          - "host.docker.internal:host-gateway"
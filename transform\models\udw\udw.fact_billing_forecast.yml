version: 2
models:
  - name: udw.fact_billing_forecast
    description: "Forecasted billing facts based on historical contract data"
    config:
      alias: fact_billing_forecast
    columns:
      - name: forecast_key
        description: "Surrogate key for the forecast record"
        tests:
          - unique
          - not_null
      - name: placement_key
        description: "Foreign key to placement dimension"
        tests:
          - not_null
          - relationships:
              to: ref('udw.dim_placement')
              field: placement_key
      - name: job_order_key
        description: "Foreign key to job order dimension"
      - name: client_key
        description: "Foreign key to client dimension"
      - name: candidate_key
        description: "Foreign key to candidate dimension"
      - name: employee_key
        description: "Foreign key to employee dimension"
      - name: forecast_date
        description: "Date of the forecasted values"
        tests:
          - not_null
      - name: series_indicator
        description: "Indicates if the record is actual or forecasted data"
      - name: avg_weekly_hours
        description: "Average weekly hours based on historical data"
      - name: avg_bill_rate
        description: "Average bill rate based on historical data"
      - name: avg_cost_rate
        description: "Average cost rate based on historical data"
      - name: projected_bill_amount
        description: "Forecasted billing amount"
      - name: projected_cost_amount
        description: "Forecasted cost amount"
      - name: projected_spread_amount
        description: "Forecasted spread (profit) amount"


# Docusaurus

## Introduction

Docusaurus is a static-site generator that builds a single-page application with fast client-side navigation, leveraging the full power of React to make your site interactive. It provides out-of-the-box documentation features but can be used to create any kind of site.

More information at [Docusaurus documentation](https://docusaurus.io/docs)

## Using Docusaurus

- Ensure you have the DBT_PROFILES_DIR environment variable set
- Ensure you have a profiles.yml (profile file) in {DBT_PROFILES_DIR} folder

## Command line

```bash
# start dev server
npm run start
# build
npm run build
# server from build
npm run serve
```
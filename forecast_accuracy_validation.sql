-- Forecast Accuracy Validation Query
-- Run this after implementing the forecast improvements to measure accuracy gains

WITH actual_weekly AS (
    SELECT 
        placement_key,
        DATE_TRUNC('week', billing_date) as week_date,
        SUM(bill_amount) as actual_weekly_amount,
        SUM(bill_hours) as actual_weekly_hours,
        AVG(bill_rate_amount) as actual_avg_rate
    FROM DLUKIC_UDW.UDW.FACT_BILLING_UNIFIED
    WHERE series_indicator = 'Actual'
        AND billing_date >= DATEADD('month', -2, CURRENT_DATE())
        AND billing_date < CURRENT_DATE()
        AND bill_amount > 0
    GROUP BY placement_key, DATE_TRUNC('week', billing_date)
),

forecast_weekly AS (
    SELECT 
        placement_key,
        DATE_TRUNC('week', forecast_date) as week_date,
        SUM(bill_amount) as forecast_weekly_amount,
        SUM(hours) as forecast_weekly_hours,
        AVG(bill_rate) as forecast_avg_rate
    FROM DLUKIC_UDW.UDW.FACT_BILLING_FORECAST
    WHERE series_indicator = 'Backtest'
        AND forecast_date >= DATEADD('month', -2, CURRENT_DATE())
        AND forecast_date < CURRENT_DATE()
    GROUP BY placement_key, DATE_TRUNC('week', forecast_date)
),

accuracy_metrics AS (
    SELECT 
        a.placement_key,
        a.week_date,
        a.actual_weekly_amount,
        f.forecast_weekly_amount,
        a.actual_weekly_hours,
        f.forecast_weekly_hours,
        
        -- Calculate percentage errors
        CASE WHEN a.actual_weekly_amount != 0 THEN 
            ABS(f.forecast_weekly_amount - a.actual_weekly_amount) / ABS(a.actual_weekly_amount) * 100 
        END as amount_error_pct,
        
        CASE WHEN a.actual_weekly_hours != 0 THEN 
            ABS(f.forecast_weekly_hours - a.actual_weekly_hours) / ABS(a.actual_weekly_hours) * 100 
        END as hours_error_pct,
        
        -- Calculate bias (positive = over-prediction, negative = under-prediction)
        CASE WHEN a.actual_weekly_amount != 0 THEN 
            (f.forecast_weekly_amount - a.actual_weekly_amount) / ABS(a.actual_weekly_amount) * 100 
        END as amount_bias_pct,
        
        CASE WHEN a.actual_weekly_hours != 0 THEN 
            (f.forecast_weekly_hours - a.actual_weekly_hours) / ABS(a.actual_weekly_hours) * 100 
        END as hours_bias_pct
        
    FROM actual_weekly a
    INNER JOIN forecast_weekly f 
        ON a.placement_key = f.placement_key 
        AND a.week_date = f.week_date
)

SELECT 
    'Overall Accuracy Metrics' as metric_category,
    COUNT(*) as total_comparisons,
    
    -- Amount accuracy
    ROUND(AVG(amount_error_pct), 1) as avg_amount_error_pct,
    ROUND(MEDIAN(amount_error_pct), 1) as median_amount_error_pct,
    ROUND(AVG(amount_bias_pct), 1) as avg_amount_bias_pct,
    
    -- Hours accuracy  
    ROUND(AVG(hours_error_pct), 1) as avg_hours_error_pct,
    ROUND(MEDIAN(hours_error_pct), 1) as median_hours_error_pct,
    ROUND(AVG(hours_bias_pct), 1) as avg_hours_bias_pct,
    
    -- Accuracy thresholds
    ROUND(SUM(CASE WHEN amount_error_pct <= 10 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as amount_within_10pct,
    ROUND(SUM(CASE WHEN amount_error_pct <= 20 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as amount_within_20pct,
    ROUND(SUM(CASE WHEN hours_error_pct <= 10 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as hours_within_10pct,
    ROUND(SUM(CASE WHEN hours_error_pct <= 20 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as hours_within_20pct,
    
    -- Average actual vs predicted values
    ROUND(AVG(actual_weekly_amount), 0) as avg_actual_amount,
    ROUND(AVG(forecast_weekly_amount), 0) as avg_forecast_amount,
    ROUND(AVG(actual_weekly_hours), 1) as avg_actual_hours,
    ROUND(AVG(forecast_weekly_hours), 1) as avg_forecast_hours

FROM accuracy_metrics
WHERE amount_error_pct IS NOT NULL

UNION ALL

-- Breakdown by amount size
SELECT 
    CASE 
        WHEN AVG(actual_weekly_amount) < 1000 THEN 'Small Amounts (<$1K)'
        WHEN AVG(actual_weekly_amount) < 5000 THEN 'Medium Amounts ($1K-$5K)'
        WHEN AVG(actual_weekly_amount) < 10000 THEN 'Large Amounts ($5K-$10K)'
        ELSE 'Very Large Amounts (>$10K)'
    END as metric_category,
    COUNT(*) as total_comparisons,
    ROUND(AVG(amount_error_pct), 1) as avg_amount_error_pct,
    ROUND(MEDIAN(amount_error_pct), 1) as median_amount_error_pct,
    ROUND(AVG(amount_bias_pct), 1) as avg_amount_bias_pct,
    ROUND(AVG(hours_error_pct), 1) as avg_hours_error_pct,
    ROUND(MEDIAN(hours_error_pct), 1) as median_hours_error_pct,
    ROUND(AVG(hours_bias_pct), 1) as avg_hours_bias_pct,
    ROUND(SUM(CASE WHEN amount_error_pct <= 10 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as amount_within_10pct,
    ROUND(SUM(CASE WHEN amount_error_pct <= 20 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as amount_within_20pct,
    ROUND(SUM(CASE WHEN hours_error_pct <= 10 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as hours_within_10pct,
    ROUND(SUM(CASE WHEN hours_error_pct <= 20 THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 1) as hours_within_20pct,
    ROUND(AVG(actual_weekly_amount), 0) as avg_actual_amount,
    ROUND(AVG(forecast_weekly_amount), 0) as avg_forecast_amount,
    ROUND(AVG(actual_weekly_hours), 1) as avg_actual_hours,
    ROUND(AVG(forecast_weekly_hours), 1) as avg_forecast_hours
FROM accuracy_metrics
WHERE amount_error_pct IS NOT NULL
GROUP BY 
    CASE 
        WHEN AVG(actual_weekly_amount) < 1000 THEN 'Small Amounts (<$1K)'
        WHEN AVG(actual_weekly_amount) < 5000 THEN 'Medium Amounts ($1K-$5K)'
        WHEN AVG(actual_weekly_amount) < 10000 THEN 'Large Amounts ($5K-$10K)'
        ELSE 'Very Large Amounts (>$10K)'
    END

ORDER BY metric_category;

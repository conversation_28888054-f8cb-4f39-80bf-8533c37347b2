{{
    config(
        materialized='table'
    )
}}

with billing_unified as (
    select *
    from {{ ref('udw.fact_billing_unified') }}
),
placement as (
    select *
    from {{ ref('udw.dim_placement') }}
),
-- First calculate the lag for billing dates
billing_with_lag as (
    select
        placement_key,
        billing_date,
        bill_hours,
        bill_rate_amount,
        bill_amount,
        spread_amount,
        lag(billing_date) over (partition by placement_key order by billing_date) as prev_billing_date
    from billing_unified
),
-- Now we can calculate the interval and aggregate
placement_billing_patterns as (
    select
        b.placement_key,
        p.start_date,
        p.estimated_end_date,
        min(b.billing_date) as first_billing_date,
        max(b.billing_date) as last_billing_date,
        count(distinct b.billing_date) as billing_date_count,
        datediff('day', min(b.billing_date), max(b.billing_date)) as billing_span_days,
        count(distinct b.billing_date) / nullif(datediff('week', min(b.billing_date), max(b.billing_date)), 0) as billings_per_week,
        -- Calculate average interval between billings
        avg(datediff('day', b.prev_billing_date, b.billing_date)) as avg_billing_interval,
        -- Analyze billing hours pattern
        avg(b.bill_hours) as avg_bill_hours,
        stddev(b.bill_hours) as stddev_bill_hours,
        min(b.bill_hours) as min_bill_hours,
        max(b.bill_hours) as max_bill_hours,
        -- Analyze billing rates pattern
        avg(b.bill_rate_amount) as avg_bill_rate,
        stddev(b.bill_rate_amount) as stddev_bill_rate,
        min(b.bill_rate_amount) as min_bill_rate,
        max(b.bill_rate_amount) as max_bill_rate,
        -- Calculate total amounts
        sum(b.bill_amount) as total_bill_amount,
        sum(b.spread_amount) as total_spread_amount,
        -- Calculate recent averages (last 3 months)
        avg(case when b.billing_date >= dateadd('month', -3, current_date()) then b.bill_hours else null end) as avg_recent_bill_hours,
        avg(case when b.billing_date >= dateadd('month', -3, current_date()) then b.bill_rate_amount else null end) as avg_recent_bill_rate,
        sum(case when b.billing_date >= dateadd('month', -3, current_date()) then b.bill_amount else 0 end) as recent_bill_amount,
        sum(case when b.billing_date >= dateadd('month', -3, current_date()) then b.spread_amount else 0 end) as recent_spread_amount
    from billing_with_lag b
    inner join placement p on b.placement_key = p.placement_key
    group by 1, 2, 3
),
-- Categorize billing patterns
billing_pattern_categories as (
    select
        *,
        -- Categorize billing interval
        case
            when avg_billing_interval between 6 and 8 then 'Weekly'
            when avg_billing_interval between 13 and 15 then 'Bi-weekly'
            when avg_billing_interval between 28 and 32 then 'Monthly'
            else 'Other'
        end as billing_interval_category,
        -- Categorize hours stability
        case
            when stddev_bill_hours / nullif(avg_bill_hours, 0) < 0.1 then 'Very Stable'
            when stddev_bill_hours / nullif(avg_bill_hours, 0) < 0.25 then 'Stable'
            when stddev_bill_hours / nullif(avg_bill_hours, 0) < 0.5 then 'Moderate Variation'
            else 'High Variation'
        end as hours_stability_category,
        -- Categorize rate stability
        case
            when stddev_bill_rate / nullif(avg_bill_rate, 0) < 0.01 then 'Very Stable'
            when stddev_bill_rate / nullif(avg_bill_rate, 0) < 0.05 then 'Stable'
            when stddev_bill_rate / nullif(avg_bill_rate, 0) < 0.1 then 'Moderate Variation'
            else 'High Variation'
        end as rate_stability_category,
        -- Flag for estimated end date validity
        case
            when estimated_end_date is null then 'Missing'
            when estimated_end_date < start_date then 'Invalid'
            when estimated_end_date < current_date() then 'Past'
            else 'Valid'
        end as estimated_end_date_status
    from placement_billing_patterns
)
select *
from billing_pattern_categories


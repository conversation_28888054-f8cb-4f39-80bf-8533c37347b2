{{ config(
    alias = 'fact_billing_forecast'
) }}

/*
==============================
Billing Forecast Fact
==============================
- Generates forecast records for eligible placements
- Projects values forward based on historical averages
- Maintains same grain as fact_billing_unified
- Generates forecasts for the entire available date range
*/

with forecast_base as (
    select
        placement_id,
        job_order_id,
        client_id,
        candidate_id,
        employee_id,
        business_center_id,
        employee_role_id,
        median_weekly_hours,
        median_bill_rate,
        median_cost_rate,
        last_billing_date,
        billing_record_count,
        avg_weekly_hours,
        stddev_weekly_hours,
        bill_rate_stddev,
        cost_rate_stddev,
        avg_bill_rate,
        avg_cost_rate,
        latest_bill_rate,
        latest_cost_rate,
        latest_hours,
        bill_rate_change_factor,
        hours_variation_coefficient,
        estimated_end_date,
        falloff_date,
        start_date,
        forecast_end_date,
        is_forecast_eligible,
        forecast_confidence_score,
        recent_spread_trend,
        recent_avg_weekly_hours,
        recent_median_weekly_hours,
        recent_max_weekly_hours,
        recent_weeks_count
    from {{ ref('prep.billing_unified_forecast') }}
    where is_forecast_eligible = true
),

-- Create a date spine for the entire available date range
date_spine as (
    select
        date_trunc('week', date) as forecast_week,
        min(date) as forecast_date
    from {{ ref('udw.dim_date') }}
    where date <= (select max(date) from {{ ref('udw.dim_date') }})
    group by date_trunc('week', date)
),

forecast_records as (
    select
        -- Generate numeric surrogate key
        row_number() over(order by f.placement_id, d.forecast_date) as forecast_key,
        f.placement_id as placement_key,
        f.job_order_id,
        f.client_id,
        f.candidate_id,
        f.employee_id,
        f.business_center_id,
        f.employee_role_id,
        d.forecast_date as forecast_date,
        -- Calculate weeks out from current date for trend adjustments
        datediff('week', date_trunc('week', current_date()), d.forecast_date) as weeks_out,
        -- Use a more realistic hours calculation that prioritizes recent patterns
        case
            -- If we have recent weekly data (last 8 weeks), prioritize it
            when f.recent_weeks_count >= 4 and f.recent_avg_weekly_hours >= 35 then least(40, f.recent_avg_weekly_hours)
            when f.recent_weeks_count >= 4 and f.recent_median_weekly_hours >= 35 then least(40, f.recent_median_weekly_hours)
            when f.recent_weeks_count >= 4 then least(40, greatest(20, f.recent_avg_weekly_hours))
            -- Fall back to latest hours if recent
            when f.latest_hours >= 35 then least(40, f.latest_hours)
            -- Fall back to historical patterns
            when f.median_weekly_hours >= 35 then least(40, f.median_weekly_hours)
            when f.avg_weekly_hours >= 35 then least(40, f.avg_weekly_hours)
            -- Conservative estimate for part-time with minimum of 20 hours for active placements
            else least(40, greatest(20, f.median_weekly_hours * 0.3 + f.avg_weekly_hours * 0.7))
        end as hours,
        -- Use a blended bill rate calculation
        (f.median_bill_rate * 0.5 + f.avg_bill_rate * 0.5) as calculated_bill_rate,
        -- Use a blended cost rate calculation
        (f.median_cost_rate * 0.5 + f.avg_cost_rate * 0.5) as calculated_cost_rate,
        calculated_bill_rate as bill_rate,
        calculated_cost_rate as cost_rate,
        hours * calculated_bill_rate as bill_amount,
        hours * calculated_cost_rate as cost_amount,

        -- Base spread amount without trend
        hours * (calculated_bill_rate - calculated_cost_rate) as base_spread_amount,

        -- Adjust spread amount with trend, more impact for longer horizons
        case
            when weeks_out <= 4 then base_spread_amount * (1 + coalesce(f.recent_spread_trend, 0) * 0.005)
            when weeks_out <= 8 then base_spread_amount * (1 + coalesce(f.recent_spread_trend, 0) * 0.01)
            else base_spread_amount * (1 + coalesce(f.recent_spread_trend, 0) * 0.015)
        end as spread_amount,
        -- Determine series indicator based on date relative to current date
        case
            when d.forecast_date < current_date() then 'Backtest'
            else 'Forecast'
        end as series_indicator,
        f.forecast_confidence_score,
        f.recent_spread_trend
    from forecast_base f
    cross join date_spine d
    where d.forecast_date <= f.forecast_end_date
      and d.forecast_date >= f.start_date -- Start from placement start date
      -- Add a sanity check to prevent unreasonable values
      and f.avg_weekly_hours <= 100 -- Cap weekly hours at 100
)

select * from forecast_records


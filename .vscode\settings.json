{
    "workbench.iconTheme": "material-icon-theme",
    "files.exclude": {
        "**/__pycache__": true,
        "dbt_modules/**": true,
        "**/node_modules/**": true,
        "website/build/**": true,
        "**/.docusaurus": true
    },
    "search.exclude": {
        "**/pbi/models/**": true
    },
    "files.associations": {
        // jinja redering for file types
        "**/transform/**/*.sql": "jinja-sql",
        "**/transform/**/*.yml": "jinja-yaml",
        // "**/transform/**/*.yaml": "jinja-yaml",
        "**/transform/**/docs/**/*.md": "jinja-md",
        // optional: don't format models in `target/` dir
        "**/.build/**": "",
    },
    "material-icon-theme.folders.color": "#ef5350",
    "material-icon-theme.showWelcomeMessage": false,
    "material-icon-theme.folders.theme": "none",
    "material-icon-theme.activeIconPack": "none",
    "material-icon-theme.files.associations": {
        "*.yaml": "yaml",
        "*.yml": "yaml"
    },
    "editor.quickSuggestions": {
        "strings": true
    },
    "workbench.editor.highlightModifiedTabs": true,
    // show whitespace as dots
    "editor.renderWhitespace": "all",
    // Tab settings
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "workbench.colorCustomizations": {
        "activityBar.activeBackground": "#005397",
        "activityBar.activeBorder": "#5F1F32",
        "activityBar.background": "#005397",
        "activityBar.foreground": "#e7e7e7",
        "activityBar.inactiveForeground": "#e7e7e799",
        "activityBarBadge.background": "#5F1F32",
        "activityBarBadge.foreground": "#e7e7e7",
        "editorGroup.border": "#005397",
        "panel.border": "#005397",
        "sash.hoverBorder": "#005397",
        "sideBar.border": "#005397",
        "statusBar.background": "#003764",
        "statusBar.foreground": "#e7e7e7",
        "statusBarItem.remoteBackground": "#1B857F",
        "statusBarItem.remoteForeground": "#e7e7e7",
        "titleBar.activeBackground": "#003764",
        "titleBar.activeForeground": "#e7e7e7",
        "titleBar.inactiveBackground": "#00376499",
        "titleBar.inactiveForeground": "#e7e7e799"
    },
    // todo-tree
    "todo-tree.general.tags": [
        "BUG",
        "HACK",
        "FIXME",
        "TODO",
        "NOTE",
        "???"
    ],
    "todo-tree.regex.regex": "(//|#|--|<!--|;|/\\*|^|^\\s*(-|\\d+.))\\s*($TAGS)",
    "todo-tree.highlights.defaultHighlight": {
        "type": "text"
    },
    "todo-tree.highlights.customHighlight": {
        "TODO": {
            "icon": "check"
        },
        "???": {
            "icon": "question"
        }
    },
    "todo-tree.general.statusBar": "tags",
    "todo-tree.highlights.backgroundColourScheme": [
        "red",
        "orange",
        "yellow",
        "green",
        "lightblue",
        "violet",
    ],
    "todo-tree.highlights.foregroundColourScheme": [
        "white",
        "black",
        "black",
        "white",
        "black",
        "black"
    ],
    "todo-tree.highlights.useColourScheme": true,
    
    "[jinja-sql]": {
        "editor.defaultFormatter": "dorzey.vscode-sqlfluff", // innoverio.vscode-dbt-power-user
        "editor.formatOnSave": false,
    },
    "[sql]": {
        "editor.defaultFormatter": "dorzey.vscode-sqlfluff", // henriblancke.vscode-dbt-formatter 
        "editor.formatOnSave": false,
    },
    "[yaml]": {
        "editor.tabSize": 2
    },
    "[yml]": {
        "editor.tabSize": 2
    },
    "[jinja-yaml]": {
        "editor.tabSize": 2
    },
    "material-icon-theme.opacity": 0.1,
    "material-icon-theme.saturation": 0.1,
    // you get this by calling `where sqlfluff` after calling `pip install sqlfluff`
    // "sql.linter.executablePath": "/usr/local/bin/sqlfluff", // use which sqlfluff
    // "sql.linter.run": "onSave", // "onType" or "onSave" if you'd like it less frequent
    "sqlfluff.config": "${workspaceFolder}/.sqlfluff",
    //"sqlfluff.dialect": "mysql",
    //"sqlfluff.excludeRules": ["L009"],
    "sqlfluff.executablePath": "sqlfluff",
    // "sqlfluff.ignoreLocalConfig": false,
    // "sqlfluff.ignoreParsing": false,
    // "sqlfluff.rules": [],
    // "sqlfluff.suppressNotifications": false,
    // "sqlfluff.workingDirectory": "",
    /* Linter */
    
    "sqlfluff.linter.run": "onSave",
    "sqlfluff.experimental.format.executeInTerminal": true,
    "dbt.queryLimit": 500,
    // "sqlfluff.linter.arguments": [],
    // "sqlfluff.linter.diagnosticSeverity": "error",
    // "sqlfluff.linter.diagnosticSeverityByRule": [
    //     {
    //     "rule": "L003",
    //     "severity": "warning"
    //     }
    // ],
    // "sqlfluff.linter.lintEntireProject": true,
    // /* Formatter */
    // "sqlfluff.format.arguments": ["--FIX-EVEN-UNPARSABLE"],
    // "sqlfluff.format.enabled": true,
    //
    // "terminal.integrated.env.[osx|windows|linux]": {
    //     "DBT_PROFILES_DIR": "/workspace/.dbt",
    // },
}

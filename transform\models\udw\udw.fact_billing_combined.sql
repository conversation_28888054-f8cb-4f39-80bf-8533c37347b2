{{ config(
    alias = 'fact_billing_combined'
) }}

/*
==============================
Billing Combined Fact
==============================
- Combines actual and forecast billing data
- Maintains consistent structure and relationships
- Uses series_indicator to distinguish between actual and forecast
*/

with actuals as (
    select
        billing_unified_key as fact_key,
        placement_key,
        job_order_key,
        client_key,
        candidate_key,
        employee_key,
        billing_date,
        bill_hours as hours,
        bill_rate_amount as bill_rate,
        cost_rate_amount as cost_rate,
        0 as split_percentage, -- Default value since we don't have this in the source
        bill_amount,
        cost_amount,
        spread_amount,
        'Actual' as series_indicator
    from {{ ref('udw.fact_billing_unified') }}
    where series_indicator = 'Actual'
),

forecast as (
    select
        billing_unified_key as fact_key, -- Changed from forecast_key to billing_unified_key
        placement_key,
        job_order_key,
        client_key,
        candidate_key,
        employee_key,
        billing_date,
        bill_hours as hours,
        bill_rate_amount as bill_rate,
        cost_rate_amount as cost_rate,
        0 as split_percentage, -- Default value since we don't have this in the source
        bill_amount,
        cost_amount,
        spread_amount,
        series_indicator
    from {{ ref('udw.fact_billing_unified') }}
    where series_indicator = 'Forecast'
),

combined as (
    select * from actuals
    union all
    select * from forecast
)

select * from combined


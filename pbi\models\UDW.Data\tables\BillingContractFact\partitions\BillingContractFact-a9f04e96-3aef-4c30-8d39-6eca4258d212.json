{"name": "BillingContractFact-a9f04e96-3aef-4c30-8d39-6eca4258d212", "mode": "import", "queryGroup": "Facts", "source": {"type": "m", "expression": ["let", "    Source = Snowflake.Databases(SNOW<PERSON>AKE_HOST,SNOWFLAKE_WAREHOUSE,[Role=SNOWFLAKE_ROLE]),", "    Database = Source{[Name=SNOWFLAKE_DATABASE,Kind=\"Database\"]}[Data],", "    Schema = Database{[Name=SNOWFLAKE_SCHEMA,Kind=\"Schema\"]}[Data],", "    Relation = Schema{[Name=\"FACT_BILLING_CONTRACT\",Kind=\"Table\"]}[Data],", "    Renamed = Table.RenameColumns(Relation,{", "        {\"BILLING_CONTRACT_KEY\", \"BillingContractKey\"},", "        {\"PLACEMENT_KEY\", \"PlacementKey\"},", "        {\"<PERSON>O<PERSON>_ORDER_KEY\", \"JobOrder<PERSON>ey\"},", "        {\"CLIENT_KEY\", \"<PERSON><PERSON><PERSON><PERSON>\"},", "        {\"CANDIDATE_KEY\", \"Candidate<PERSON><PERSON>\"},", "        {\"BILLING_DATE\", \"BillingDate\"},", "        {\"EMPLOYMENT_ID\", \"EmploymentId\"},", "        {\"BILL_RATE_AMOUNT\", \"BillRateAmount\"},", "        {\"COST_RATE_AMOUNT\", \"CostRateAmount\"},", "        {\"LOAD_PER\", \"LoadPer\"},", "        {\"BILL_HOURS\", \"BillHours\"},", "        {\"NOBILL_HOURS\", \"NobillHours\"},", "        {\"ADJUST_HOURS\", \"AdjustHours\"},", "        {\"GROSS_AMOUNT\", \"GrossAmount\"},", "        {\"BILL_AMOUNT\", \"BillAmount\"},", "        {\"COST_AMOUNT\", \"CostAmount\"},", "        {\"SPREAD_AMOUNT\", \"SpreadAmount\"}", "    }),", "    PartitionDate = Table.DuplicateColumn(Renamed, \"BillingDate\", \"PartitionDate\"),", "    PartitionDateType = Table.TransformColumnTypes(PartitionDate,{{\"PartitionDate\", type datetime}}),", "    PartitionDateFilter = Table.SelectRows(PartitionDateType, each [PartitionDate] >= RangeStart and [PartitionDate] < RangeEnd),", "    RemovePartitionDate = Table.RemoveColumns(PartitionDateFilter,{\"PartitionDate\"})", "in", "    RemovePartitionDate"]}}
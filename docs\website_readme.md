# Website

## Deployment

```bash
sudo apt-get update
sudo apt-get install nginx
#apt install -y certbot

#
cd /workspace
dbt docs generate
cd website && npm run build && cd ..
# www target directory
www=/var/www/html
mkdir -p $www & mkdir -p $www/dbt
# clear folder
rm -r $www/*
# copy dbt docs under dbt
cp -r .build www/dbt/
# copy docusaurus as root
cp -r website/build/** www

# copy static assets to www folder
cp -r /workspace/website/build/** $www
cp -r /workspace/.build/** $www/dbt
sudo service nginx start

#apt-get install -y python3-certbot-nginx

```
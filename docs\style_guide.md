# Conventions & Style guide

**OPTIMIZE FOR REA<PERSON><PERSON><PERSON><PERSON>Y AND UNDERSTANDABILITY NOT SHORTER CODE OR NAME**
**CODE IS CHEAP, <PERSON>AIN TIME IS EXPENSIVE**
**CONSISTENCY IS KEY**

## General naming convention

- Names should be as verbose as needed to convey what they do
  - As much as possible name object descriptively
  - Avoid ambiguity
  - Try to avoid abbreviations unless reasonably well known and commonly used
- Names should start with a letter
- Avoid reserved words and keywords as names
- Singular nouns are preferred to avoid confusing pluralization
- Lowercase when no case is needed
- Snake case for readability
- Use names based on the establishied business terminology

### Lists

- Ordered lists and hierarchies hould follow their natural order
- If not natural order exists alphabethical order is recommended

## Folder & file Naming

- snake case
- lower case file
- use prefix and/or suffix for easier searchability
- organize into folders
- prefer < 30 files in folder

## Database Style Guide

### Database object naming

- Booleans should be prefixed with is_ or has_
- Money/currency fields should be decimal currency (19.99 for $19.99)
- Timestamp columns should be named <event>_at, e.g. created_at, and should be in UTC. If a different timezone is being used, this should be indicated with a suffix, e.g created_at_pt
- Use the same field names across objects where possible, e.g. a key to the customers table should be named customer_id rather than user_id

## DBT style guide

### Naming style & conventions

- Each model should have a primary key
- Models with confusing or noteable logic should be commented

### Model configuration

### Testing

### SQL Style guide

- Use trailing commas
- Field names and function names should all be lowercase
- The as keyword should be used when aliasing a field or table
- Be explicit about your join (i.e. write inner join instead of join)
- Avoid aliasing with abbrevations

##### SQL Linting

To help ensure consistency and quality of sql models/files you can use SQLFluff (included in dev env setup) to check and fix sql files.

Use the following commands to lint sql files:

```bash
# lint file
sqlfluff lint {file.sql}
# fix file
sqlfluff fix {file.sql}
```
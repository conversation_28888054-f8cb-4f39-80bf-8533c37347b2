# Power BI

## Introduction

**PBI** is an analytics platform that enables dataset and semantic layer definition, reporting and interactive analysis.

This is a working document meant to guide and document our implementation.


## Architecture
![diagram](/docs/assets/system_diagram_1.png)


## Working with PBI

### Desktop (Local) vs Workspace (Online)
There are two main contexts for PBI work, illustrated in the diagram above. 

In the desktop context we are working with the desktop application, creating both the dataset/semantic model and reports. Each working file can be published to the online workspace. Note that the dataset file pulls data from our UDW store, and that that our report file pulls data from the published PBI dataset.

In the online workspace context we access reports and datasets through a web browser, and they are shared resources.


### PBIX vs PBIT
Because PowerBI desktop files (PBIX) by contain access credentials, by default we do not store them in our project repository (it is set to ignore them). 

For commiting PowerBI work to our repository we are using PowerBI Templates (PBIT). A PowerBI template is essentially a shell of the full file, but with the data and credentials stripped out. 

Our general workflow will be to work with and publish to the online workspace PBIX files. We then save them as PBIT templates when we are ready to commit out work, and commit the templates only to the project repository. 

When getting the latest code the process is reversed: we pull the latest PBIT templates from the repository, and the first time we open them we will be asked to fill in the missing credentials. With the credentials supplied, the template will pull in the data and become a PBIX file which we can now work with. 

More info on PBI Templates [here](https://docs.microsoft.com/en-us/power-bi/create-reports/desktop-templates).

#### Connecting to Snowflake

The first time you open a PBIT file you will be asked to supply credentials to connect to our snowflake instance. This information should be in your personal .env file in the project root directory.


### PowerBI Dataset vs Report
Per diagram above, in our solution we are separating the dataset from reports. 

The dataset pulls data from the database and with that data establishes the semantic model that will power our reporting. In this layer we: 
- Rename tables and fields for user clarity and comfort
- Define relationships between tables/entities
- Create DAX measures that calculate dynamically in a users context
- Organize data for accessability

The report on the other hand accesses the published dataset, and in this layer:
- Create reusable reporting and visualization
- Define report-level formatting
- Perform spot analysis
- Grant access

More info on PBI Basic Concepts for [Designers](https://docs.microsoft.com/en-us/power-bi/fundamentals/service-basic-concepts) and [End Users](https://docs.microsoft.com/en-us/power-bi/consumer/end-user-basic-concepts).

More info on DAX [here](https://docs.microsoft.com/en-us/dax/).

## Design and Organization

### Separation of Dataset and Reporting
Covered above, core to our design is a central semantic dataset layer which then feeds all reporting.  

### Dataset / Semantic Model

### Star Schema
We fundementally arrainge our data in a star schema for coherence, and due to it's compatability with the PBI semantic engine. 

More info on Star Schema conceps [here](https://www.geeksforgeeks.org/star-schema-in-data-warehouse-modeling/). 

### User Experience Perspective
Much of our design flows from the experience we would like to create for our users. In this case report creators and self-service end users. We would like for them to experience the following when creating reports:
- Familiar but unambigious business terminology
- Metrics organized together and in groups (i.e. all the things we can count/sum/avg)
- Entities that we can use to cut our metrics (i.e. business centers, clients, employees, etc.)

#### Practically:
- Friendly casing
- All UDW Dimensions renamed to entity names
- All UDW Facts hidden and exposed through DAX measures
- All IDs, Keys, enums, codes, etc. are hidden


#### Field Naming Convetion
Want to strike the right balance between intuitive readability and formal structure (all field names must be globally unique). 

Proposal:   
{Entity Name} {Field Name} (TimeFrame, Aggregation)  
Job Order Positions (Cnt)  
Job Order Positions (Weekly Avg)  
Job Order Positions (13wk Avg) 


Lean towards explicit and verbose  
Short-names for aggregations  
No field name when counting table entities  

#### Fonts and Colors
Need to decide on standard font sizes and colors. 

Proposal:
- 14pt for main content text and axis
- 16pt for widget titles
- 24pt for page headers

Colors:
- Judge has standard reporting pallete?

Report Dimensions:
- 1920x1080?

#### Standard Filters:
Need a list of standard filters to apply to (all?) reports. Can start with the ones we make available in JEMS.

## Administration

### Workspace Setup
The fine-tune setting of a workspace are decisions that need to be made in line with organizational standards/desires. High level elements to consider: 

- Personal workspaces vs shared workspace
- Shared workspace scope - one workspace for everything, or?
    - Audience based scope? (who can see what? ties into permissions below)
    - Stage scope? (i.e. dev, stage, prod)
- Naming of workspaces
- Who to give permissions to, to what and how?
- Refresh/pipelines

Some good guidance around points above is [here](https://data-marc.com/2022/01/18/power-bi-workspace-setup-part-1/) and [here](https://data-marc.com/2022/02/01/power-bi-workspace-setup-part-2/).


## Additional Resources
[PowerBI Videos](https://docs.microsoft.com/en-us/power-bi/fundamentals/videos)


### Technical References

- [Power BI cmdlets](https://docs.microsoft.com/en-us/powershell/power-bi/overview?view=powerbi-ps )
- [Power BI REST APIs](https://docs.microsoft.com/en-us/rest/api/power-bi/)

### Code samples
https://github.com/PowerBiDevCamp/TenantManagement/blob/main/README.md
https://martinschoombee.com/2020/09/22/automating-power-bi-deployments-connecting-to-the-service/
https://github.com/mschoombee/Blog/tree/main/2020/automating-power-bi-deployments
https://github.com/PowerBiDevCamp/PowerBI-PowerShell-Tutorial/tree/master/Solution
https://martinschoombee.com/2020/09/15/automating-power-bi-deployments-a-series/
# Financial Forecasting Implementation Prompt

## Context
You are working on a short-term financial forecasting project that extends existing billing data into the future using historical patterns. The core dbt models already exist and just need adjustments to support backtesting and improved accuracy validation.

## Project Files Structure
```
1_shorttermfinancialforecast/
├── investigation.md          # Document findings and insights here
├── planning.md              # Detailed implementation steps
├── checklist.md             # Track progress with checkboxes
└── [other project files]

transform/models/
├── prep/prep.billing_unified_forecast.sql     # Core forecast logic (EXISTS - needs adjustment)
├── udw/udw.fact_billing_unified.sql          # Main fact table (EXISTS - needs integration)
├── udw/udw.fact_billing_forecast.sql         # Separate forecast table (EXISTS - may deprecate)
└── udw/udw.fact_billing_forecast_backtest.sql # Backtest model (EXISTS - planning to replace)
```

## Current State & Objectives

### What Exists:
- **prep.billing_unified_forecast**: Analyzes historical patterns and determines placement eligibility for forecasting
- **udw.fact_billing_forecast**: Generates weekly forecast records for eligible placements
- **udw.fact_billing_unified**: Already combines actual and forecast billing data
- All dbt models are built and working with current forecast logic

### What Needs Adjustment:
1. **Make forecast models flexible** for backtesting (configurable start dates in prep layer)
2. **Add series indicators** to distinguish actual vs forecast vs backtest data
3. **Build systematic accuracy validation** comparing forecast vs actual
4. **Create hero demo report** showing past actuals + future forecasts
5. **Update existing DAX metrics** to properly handle forecast vs actual data

## Implementation Instructions

### Phase 1: Enhance Forecast Pipeline Flexibility
**Files**: 
- `transform/models/prep/prep.billing_unified_forecast.sql` (eligibility analysis)
- `transform/models/udw/udw.fact_billing_forecast.sql` (forecast generation)

**Goal**: Make the forecast pipeline accept configurable start dates for backtesting

**Tasks**:
1. **In prep.billing_unified_forecast.sql**:
   - Add `forecast_start_date` parameter (default to current date)
   - Modify eligibility analysis to work from any historical date
   - Ensure pattern analysis uses data up to the specified start date only

2. **In udw.fact_billing_forecast.sql**:
   - Use the prep model's configurable start date
   - Add `series_type` field with values: 'Forecast', 'Backtest'
   - Generate forecasts from the specified start date forward
   - Maintain all existing business logic and grain

**Current Architecture Understanding**:
- prep model determines WHO gets forecasted and their historical patterns
- fact_billing_forecast model generates the WEEKLY forecast records
- fact_billing_unified already combines both actual and forecast data

### Phase 2: Build Accuracy Validation Framework
**Primary Focus**: Leverage existing forecast pipeline for backtesting

**Goal**: Create systematic comparison between forecast and actual data

**Tasks**:
1. **Generate Backtest Data**:
   - Run the forecast pipeline with `forecast_start_date` = beginning of year
   - This should flow through: prep → fact_billing_forecast → fact_billing_unified
   - Tag backtest forecasts with `series_type = 'Backtest'`

2. **Compare Forecast vs Actual**:
   - Query fact_billing_unified to compare backtest forecasts against actual data
   - Calculate accuracy metrics:
     - Weekly variance: `(forecast_spread - actual_spread) / actual_spread * 100`
     - Placement-level accuracy averages
     - Overall model accuracy baseline
   - Identify which placements forecast well vs poorly

3. **Document Analysis**:
   - Record findings in `investigation.md`
   - Focus on: Are errors concentrated in specific placements or distributed?
   - Which business factors correlate with forecast accuracy?

**Key Insight**: Since fact_billing_unified already combines data, the comparison analysis should be straightforward once series_type indicators are added.

### Phase 3: Enhance Data Model Integration
**File**: `transform/models/udw/udw.fact_billing_unified.sql`

**Goal**: Add series type indicators and optimize the existing actual+forecast combination

**Tasks**:
1. **Add Series Type Field**:
   - Ensure `series_type` column exists with values: 'Actual', 'Forecast', 'Backtest'
   - Actual data should be tagged as `series_type = 'Actual'`
   - Forecast data from fact_billing_forecast should be tagged appropriately

2. **Validate Current Integration**:
   - Verify how fact_billing_forecast data currently flows into fact_billing_unified
   - Ensure no duplication between actual and forecast records
   - Test that existing grain and dimensions are preserved

3. **Optimize for Backtesting**:
   - Ensure backtest forecast data (when generated) integrates properly
   - Maintain ability to have overlapping time periods with different series types
   - Test incremental loading behavior with new series types

**Current Architecture Advantage**: Since integration already exists, focus is on adding series indicators and ensuring backtest data flows through properly.

### Phase 4: Update Metrics and Reporting
**DAX Metrics Updates**

**Goal**: Modify existing metrics to properly handle actual vs forecast data

**Tasks**:
1. Audit all existing billing unified metrics
2. Add series_type filters to exclude forecast data by default
3. Create forecast-inclusive versions of key metrics
4. Test "Spread focused" metrics specifically
5. Ensure backward compatibility with existing reports

**Hero Demo Report**

**Goal**: Create compelling visualization showing historical context + future projections

**Requirements**:
- Time series: 6 months actual + 3 months forecast
- Interactive time period slider
- Client and business center breakouts
- Major metrics across the top
- Clear visual separation between actual and forecast

### Phase 5: Validation and Refinement

**Goal**: Ensure accuracy and optimize based on findings

**Tasks**:
1. Run full accuracy validation on integrated model
2. Adjust forecast logic based on accuracy findings
3. Implement monitoring for ongoing accuracy tracking
4. Document lessons learned and future improvements

## Working Approach

### Step-by-Step Execution:
1. **Understand current architecture** - Document how the 3-model pipeline currently works in `investigation.md`
2. **Enhance prep model** - Add configurable start date for eligibility analysis
3. **Enhance fact_billing_forecast** - Add series_type and use configurable dates
4. **Verify fact_billing_unified** - Ensure it properly combines data with series indicators
5. **Run accuracy analysis** - Generate backtests and compare vs actuals
6. **Update metrics** - Ensure existing reports work, create forecast-aware metrics
7. **Build demo report** - Create compelling visualization
8. **Validate and refine** - Test accuracy, optimize, document

### Documentation Strategy:
- **investigation.md**: Record current architecture understanding, findings, and insights
- **planning.md**: Maintain detailed implementation steps and decisions
- **checklist.md**: Track progress and ensure nothing is missed
- **Code comments**: Document all model changes and business logic

### Current Pipeline Flow:
```
prep.billing_unified_forecast (eligibility + patterns)
         ↓
udw.fact_billing_forecast (weekly forecast records)
         ↓
udw.fact_billing_unified (actual + forecast combined)
```

### Key Success Criteria:
- Forecast accuracy within acceptable variance (establish baseline first)
- Seamless integration preserving existing functionality
- Compelling demo showing value of forecasting capability
- Robust framework for ongoing accuracy monitoring

## Prompt for Each Phase:

### When Starting Each Phase:
```
I'm working on Phase [X] of the financial forecasting project. 

Current objective: [specific phase goal]

Files to work with: [specific files for this phase]

Current architecture: prep.billing_unified_forecast → udw.fact_billing_forecast → udw.fact_billing_unified

Key questions I need to answer: [phase-specific questions]

Please help me [specific technical task] while ensuring I maintain the existing 3-model pipeline and [specific requirements].

Document any findings in investigation.md and update checklist.md as I complete tasks.
```

### When Encountering Issues:
```
I'm working on [specific task] and encountering [specific issue].

Current code/logic: [paste relevant code]

Expected behavior: [what should happen]

Actual behavior: [what's happening]

Context: This is part of [phase] where I'm trying to [goal].

Please help me troubleshoot and suggest solutions while maintaining [requirements].
```

### When Documenting Findings:
```
I just completed [specific analysis/task] and found [key findings].

Please help me document this properly in investigation.md and suggest what this means for [next steps/decisions].

The findings are: [detailed findings]

This relates to [broader project goals] and might impact [specific areas].
```

## Key Reminders:

1. **Existing models work** - you're enhancing, not rebuilding
2. **Boss emphasized accuracy** - systematic validation is crucial
3. **No minimum data requirements** - use all available historical data
4. **Backward compatibility** - existing reports must continue working
5. **Unified approach** - consolidate rather than separate actual/forecast data
6. **Documentation is key** - record insights and decisions throughout

Start with Phase 1 and work systematically through each phase, documenting findings and updating progress as you go.
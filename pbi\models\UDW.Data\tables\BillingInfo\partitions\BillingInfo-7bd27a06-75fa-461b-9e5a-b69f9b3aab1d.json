{"name": "BillingInfo-7bd27a06-75fa-461b-9e5a-b69f9b3aab1d", "mode": "import", "queryGroup": "Dimensions", "source": {"type": "m", "expression": ["let", "    Source = Snowflake.Databases(SNOW<PERSON>AKE_HOST,SNOWFLAKE_WAREHOUSE,[Role=SNOWFLAKE_ROLE]),", "    Database = Source{[Name=SNOWFLAKE_DATABASE,Kind=\"Database\"]}[Data],", "    Schema = Database{[Name=SNOWFLAKE_SCHEMA,Kind=\"Schema\"]}[Data],", "    Relation = Schema{[Name=\"DIM_BILLING_INFO\",Kind=\"Table\"]}[Data],", "    Renamed = Table.RenameColumns(Relation,{", "        {\"BILLING_INFO_KEY\", \"BillingInfoKey\"},", "        {\"PAY_CODE\", \"PayCode\"}", "    })", "in", "    <PERSON>amed"]}}
build a prep billing unified forecast that’s backdated to start of year.

compare differences between that and actual billing unified in total spread per week per placement, are our differences uniform or are we missing some placements more than others?

Create a simple sql based accuracy calc that compliments above analysis, we want to answer: for a given week what percent of spread money are we off by? Also same question but at a placement + 
week level. The weak percent, and that averaged over all weeks in our lookback test will give us a baseline. Sorting placements by percent off will quickly tell us which ones are the problem (or they all are). 

Consider adjusting core forecast prep logic depending on what we find.
# DBT

## Introduction

**dbt** (data build tool) enables analytics engineers to transform data in their warehouses by simply writing select statements. dbt handles turning these select statements into tables and views.

dbt also enables data engineers, dbas and data analysts to work more like software engineers and follow modern software engineering practices like
- version control
- quality assurance and code testing
- CI/CD and automation
- documentation
- maintainability, modularity and extendibility

## Using DBT

- Ensure you have the DBT_PROFILES_DIR environment variable set
- Ensure you have a profiles.yml (profile file) in {DBT_PROFILES_DIR} folder

## Command line cheat sheet

This is a simplified version of the [primary command reference](https://docs.getdbt.com/reference/dbt-commands/).

dbt specific:

- `dbt clean` - this will remove the /dbt_modules (populated when you run deps) and /target folder (populated when models are run)
- `dbt run` - regular run - runs all models
- `dbt run --select` - model selection syntax. Specifying models can save you a lot of time by only running/testing the models that you think are relevant. However, there is a risk that you'll forget to specify an important upstream dependency so it's a good idea to understand the syntax thoroughly:

  - `dbt run --select my_dbt_project_name` - runs all models in your project
  - `dbt run --select my_dbt_model` - runs a specific model
  - `dbt run --select path.to.my.models` - runs all models in a specific directory
  - `dbt run --select my_package.some_model` - run a specific model in a specific package
  - `dbt run --select tag:nightly` - run models with the "nightly" tag
  - `dbt run --select path/to/models` - run models contained in path/to/models
  - `dbt run --select path/to/my_model.sql` - run a specific model by its path
  - dbt supports a shorthand language for defining subsets of nodes. This language uses the characters +, @, *, and ,
    - `dbt run --select +modelname` - runs modelname and all parents
    - `dbt run --select modelname+` - runs modelname and all children
    - `dbt run --select +modelname+` - runs modelname, and all parents and children
    - `dbt run --select @modelname` - runs modelname, all parents, all children, AND all parents of all children
    - `dbt run --exclude modelname` - will run all models except modelname
- `dbt run --full-refresh` - will refresh incremental models
- `dbt test` - will run custom data tests and schema tests; TIP: dbt test takes the same --model and --exclude syntax referenced for dbt run
- `dbt seed` - will load csv files specified in the data-paths directory into the data warehouse. Also see the seeds section of this guide
- `dbt compile` - compiles all models. This isn't a command you will need to run regularly. dbt will compile the models when you run any models.
- `dbt docs generate` - generates dbt docs
- `dbt docs serve` - starts a webserver on port 8000 to serve your documentation locally (you may specify a different port with `--port` flag)

## DBT addons & extensions

### dbt-invoke

You can use [dbt-invoke](https://github.com/Dashlane/dbt-invoke) to create, update and delete dbt yml property file entries
Considerations
 - You must have previously executed dbt run/dbt seed/dbt snapshot on the resources for which you wish to create/update property files.
 - Property files will be created, updated, or deleted on a one-to-one basis in the same paths as the resourc

```bash
# update properties for udw.job_order
dbt-invoke properties --models udw.job_order
```

### dbt_profiler

Profiling database data, enables ad-hoc profile or embedd into doc blocks
https://hub.getdbt.com/data-mie/dbt_profiler/latest/

```bash
# print profile for a table
dbt run-operation print_profile --args '{"relation_name": "calendar", "schema":"ods"}'
```


## Additional Resources

- Learn more about dbt [in the docs](https://docs.getdbt.com/docs/introduction)
- DBT style guide [dbt style guide](https://github.com/dbt-labs/corp/blob/master/dbt_style_guide.md)
- Check out [Discourse](https://discourse.getdbt.com/) for commonly asked questions and answers
- Join the [chat](https://community.getdbt.com/) on Slack for live discussions and support
- Find [dbt events](https://events.getdbt.com) near you
- Check out [the blog](https://blog.getdbt.com/) for the latest news on dbt's development and best practices

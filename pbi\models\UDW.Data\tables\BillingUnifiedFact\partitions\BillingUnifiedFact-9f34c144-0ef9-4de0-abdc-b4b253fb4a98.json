{"name": "BillingUnifiedFact-9f34c144-0ef9-4de0-abdc-b4b253fb4a98", "mode": "import", "queryGroup": "Facts", "source": {"type": "m", "expression": ["let", "    Source = Snowflake.Databases(SNOW<PERSON>AKE_HOST,SNOWFLAKE_WAREHOUSE,[Role=SNOWFLAKE_ROLE]),", "    Database = Source{[Name=SNOWFLAKE_DATABASE,Kind=\"Database\"]}[Data],", "    Schema = Database{[Name=SNOWFLAKE_SCHEMA,Kind=\"Schema\"]}[Data],", "    Relation = Schema{[Name=\"FACT_BILLING_UNIFIED\",Kind=\"Table\"]}[Data],", "    Renamed = Table.RenameColumns(Relation,{", "        {\"BILLING_UNIFIED_KEY\", \"BillingUnifiedKey\"},", "        {\"BILLING_INFO_KEY\", \"BillingInfoKey\"},", "        {\"LINE_OF_BUSINESS_KEY\", \"LineOfBusinessKey\"},", "        {\"PLACEMENT_KEY\", \"PlacementKey\"},", "        {\"<PERSON>O<PERSON>_ORDER_KEY\", \"JobOrder<PERSON>ey\"},", "        {\"CLIENT_KEY\", \"<PERSON><PERSON><PERSON><PERSON>\"},", "        {\"CANDIDATE_KEY\", \"Candidate<PERSON><PERSON>\"},", "        {\"EMPLOYEE_KEY\", \"EmployeeK<PERSON>\"},", "        {\"EMPLOYEE_ROLE_KEY\", \"EmployeeR<PERSON>K<PERSON>\"},", "        {\"BUSINESS_CENTER_KEY\", \"BusinessCenterKey\"},", "        {\"BUSINESS_CENTER_ROLE_KEY\", \"BusinessCenterRoleKey\"},", "        {\"BILLING_DATE\", \"BillingDate\"},", "        {\"GROSS_AMOUNT\", \"GrossAmount\"},", "        {\"BILL_AMOUNT\", \"BillAmount\"},", "        {\"COST_AMOUNT\", \"CostAmount\"},", "        {\"SPREAD_AMOUNT\", \"SpreadAmount\"},", "        {\"BILL_HOURS\", \"BillHours\"},", "        {\"NOBILL_HOURS\", \"NoBillHours\"},", "        {\"ADJUST_HOURS\", \"AdjustHours\"}", "    }),", "    PartitionDate = Table.DuplicateColumn(Renamed, \"BillingDate\", \"PartitionDate\"),", "    PartitionDateType = Table.TransformColumnTypes(PartitionDate,{{\"PartitionDate\", type datetime}}),", "    PartitionDateFilter = Table.SelectRows(PartitionDateType, each [PartitionDate] >= RangeStart and [PartitionDate] < RangeEnd),", "    RemovePartitionDate = Table.RemoveColumns(PartitionDateFilter,{\"PartitionDate\"})", "in", "    RemovePartitionDate"]}}
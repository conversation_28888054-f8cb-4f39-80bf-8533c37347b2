# Generic reusable UDW build pipeline template

parameters:
- name: variableGroup
  displayName: Target Variable Group
  type: string
  default: PROD
  values:
  - PROD
  - CICD

variables:
- group: ${{parameters.variableGroup}} # variable group
- name: specFile
  value: $(SNOWFLAKE_BOT_SPEC_FILE)

steps:
- template: templates/python_setup_template.yml
- template: templates/dbt_setup_template.yml
- script: |
    dbt debug
    dbt seed
    dbt run
  displayName: 'Run dbt'
  env:
    SNOWFLAKE_TRANSFORM_PASSWORD: $(SNOWFLAKE_TRANSFORM_PASSWORD)
- template: templates/permifrost_bot_template.yml
  parameters:
    specFile: ${{ variables.specFile }}
    action: run
- script: |
    dbt test || true
  displayName: 'Run dbt tests'
  env:
    SNOWFLAKE_TRANSFORM_PASSWORD: $(SNOWFLAKE_TRANSFORM_PASSWORD)
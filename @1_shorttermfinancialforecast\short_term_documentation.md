# Short-Term Financial Forecast Models Documentation

This document provides an overview of the dbt models used for short-term financial forecast generation, backtesting, and accuracy validation.

## 1. `prep.billing_unified_forecast`

*   **Purpose:** This model analyzes historical billing patterns from `prep.billing_unified` to determine forecast eligibility, calculate aggregated metrics (median, average, standard deviation for hours, bill rate, cost rate), and derive a `recent_spread_trend`. It serves as the foundational data source for the `udw.fact_billing_forecast` model.
*   **Key Logic & Parameters:**
    *   **`forecast_start_date` variable:** Used to filter historical data, ensuring only data prior to the `forecast_start_date` is used for calculations. This is crucial for backtesting.
    *   **Data Quality Flags & Filters:**
        *   Filters out negative hours/rates, high bill/cost rate outliers (bill rate >= 5000, cost rate >= 1000).
        *   Filters out `bill_hours >= 80` (high hours outliers) to prevent inflation of average/median hours.
        *   Excludes records with 0 hours but non-zero spread amounts.
    *   **`last_billing_date`:** Maximum billing date for a placement, used in eligibility checks.
    *   **`billing_record_count`:** Count of billing records for a placement.
    *   **`recent_spread_trend`:** Calculated as the average weekly percentage change over the last 8 weeks.
        *   **Refinement:** The calculation includes `NULLIF(prev_week_spread, 0)` to prevent division by zero.
        *   **Refinement:** The trend is capped between `-2.0` and `2.0` to prevent extreme, unrealistic values from skewing the forecast.
    *   **`is_forecast_eligible`:** Boolean flag determining if a placement is suitable for forecasting.
        *   **Criteria:**
            *   `median_weekly_hours > 0`
            *   `median_bill_rate > 0`
            *   `last_billing_date >= dateadd('month', -6, forecast_start_date)` (recent activity within 6 months)
            *   `falloff_date` is null or `falloff_date > forecast_start_date`
            *   `start_date <= forecast_start_date` (contract has started)
            *   `billing_record_count >= 1` (minimum records for historical context).
            *   **Refinement:** `hours_variation_coefficient` and `bill_rate_stddev` checks were initially enabled and then commented out to loosen eligibility and increase forecast coverage.
    *   **Deduplication:** Uses `QUALIFY ROW_NUMBER() OVER (PARTITION BY placement_id ORDER BY last_billing_date DESC) = 1` in the final `SELECT` statement to ensure a unique record per `placement_id`, preventing duplicate forecasts.

## 2. `udw.fact_billing_forecast`

*   **Purpose:** This model generates weekly forecast records for eligible placements based on the aggregated metrics from `prep.billing_unified_forecast`. It projects future billing data (hours, rates, amounts, spread) for a specified horizon.
*   **Key Logic & Parameters:**
    *   **`forecast_start_date` variable:** Configurable parameter that dictates the start date for generating forecast records. When set to a historical date, it enables backtesting.
    *   **`series_type_value`:** Dynamically set to `'Forecast'` for current runs or `'Backtest'` for historical `forecast_start_date` runs.
    *   **`date_spine` CTE:** Creates a weekly date series for the forecast horizon (3 months from `forecast_start_date`).
    *   **`hours` Calculation:**
        *   **Refinement:** Simplified to an equal blend of `median_weekly_hours * 0.5 + avg_weekly_hours * 0.5`.
        *   **Refinement:** Capped at `least(40, ...)` to prevent unrealistic high hour forecasts.
    *   **`calculated_bill_rate` and `calculated_cost_rate` Calculations:**
        *   **Refinement:** Simplified to an equal blend of `median_bill_rate * 0.5 + avg_bill_rate * 0.5` and `median_cost_rate * 0.5 + avg_cost_rate * 0.5` respectively. Removed reliance on `latest_` values and `weeks_out` for stability.
    *   **`base_spread_amount`:** Calculated as `hours * (calculated_bill_rate - calculated_cost_rate)`.
    *   **`spread_amount` Calculation:**
        *   Adjusts `base_spread_amount` using `recent_spread_trend`.
        *   **Refinement:** Multipliers for `recent_spread_trend` were progressively reduced (from `0.1, 0.3, 0.5` down to `0.0001, 0.0003, 0.0005`, and then slightly increased to `0.005, 0.01, 0.015`) to fine-tune its impact and prevent overestimation or underestimation.

## 3. `udw.fact_billing_unified`

*   **Purpose:** This model unifies actual billing data from `prep.billing_unified` with forecast data from `udw.fact_billing_forecast`. It adds a `series_indicator` to actuals and combines both datasets to provide a comprehensive view for reporting and backtesting.
*   **Key Logic:**
    *   **`billing` CTE:** Selects all columns from `prep.billing_unified` and adds `'Actual'` as `series_indicator`.
    *   **`forecast` CTE:** Selects forecast data from `udw.fact_billing_forecast`, mapping relevant fields and adding `series_indicator` (which will be `'Forecast'` or `'Backtest'` from the source model).
    *   **`combined_billing_pre_id` CTE:** Performs a `UNION ALL` between the `billing` and `forecast` CTEs.
        *   **Filtering:** Only includes forecast data for the current week and beyond (for `'Forecast'` series) or all historical data (for `'Backtest'` series). This ensures backtest data is available for historical analysis.
    *   **`combined_billing` CTE:** Generates a unique numeric ID (`billing_unified_id`) after the union.
    *   **Final `SELECT`:** Joins with `udw.dim_employee` and `udw.dim_employee_tenure` to enrich the combined billing data.
    *   **Role in Backtesting:** This model is critical for backtesting as it brings actual and backtest data into the same structure, allowing for direct comparison and accuracy metric calculation. 
{"name": "BillingPermFact-49d2c3ef-8cc4-4000-86b0-4f56101c4005", "mode": "import", "queryGroup": "Facts", "source": {"type": "m", "expression": ["let", "    Source = Snowflake.Databases(SNOW<PERSON>AKE_HOST,SNOWFLAKE_WAREHOUSE,[Role=SNOWFLAKE_ROLE]),", "    Database = Source{[Name=SNOWFLAKE_DATABASE,Kind=\"Database\"]}[Data],", "    Schema = Database{[Name=SNOWFLAKE_SCHEMA,Kind=\"Schema\"]}[Data],", "    Relation = Schema{[Name=\"FACT_BILLING_PERM\",Kind=\"Table\"]}[Data],", "    Renamed = Table.RenameColumns(Relation,{", "        {\"BILLING_PERM_KEY\", \"BillingPermKey\"},", "        {\"PLACEMENT_KEY\", \"PlacementKey\"},", "        {\"<PERSON>O<PERSON>_ORDER_KEY\", \"JobOrder<PERSON>ey\"},", "        {\"CLIENT_KEY\", \"<PERSON><PERSON><PERSON><PERSON>\"},", "        {\"CANDIDATE_KEY\", \"Candidate<PERSON><PERSON>\"},", "        {\"BUSINESS_CENTER_KEY\", \"BusinessCenterKey\"},", "        {\"TRANS_DATE\", \"TransactionDate\"},", "        {\"AMOUNT\", \"Amount\"}", "    }),", "    PartitionDate = Table.DuplicateColumn(Renamed, \"TransactionDate\", \"PartitionDate\"),", "    PartitionDateType = Table.TransformColumnTypes(PartitionDate,{{\"PartitionDate\", type datetime}}),", "    PartitionDateFilter = Table.SelectRows(PartitionDateType, each [PartitionDate] >= RangeStart and [PartitionDate] < RangeEnd),", "    CleanupColumns = Table.RemoveColumns(PartitionDateFilter,{\"PartitionDate\"})", "in", "    CleanupColumns"]}}
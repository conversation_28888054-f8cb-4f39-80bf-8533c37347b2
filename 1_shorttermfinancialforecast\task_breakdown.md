# Short-Term Financial Forecast: Technical Task Breakdown

## Current Implementation Analysis

### Existing Forecast Methodology

Based on initial code review, the current forecasting system:

1. Uses historical billing data (typically last 3 months) to establish patterns
2. Calculates median weekly hours and rates to minimize impact of outliers
3. Determines forecast eligibility based on recent activity and data stability
4. Projects values forward using simple extrapolation rather than time series modeling
5. Combines actual and forecast data in a unified view

Key components in the current implementation:

- **prep.billing_unified_forecast**: Analyzes historical patterns and determines eligibility
- **udw.fact_billing_forecast**: Generates weekly forecast records for eligible placements
- **udw.fact_billing_unified**: Combines actual and forecast billing data

### Current Forecast Eligibility Criteria

The system currently determines forecast eligibility using these criteria:
- Recent billing activity (within last 3 months)
- Positive median weekly hours and rates
- Sufficient historical data points (minimum 4 billing records)
- Placement is active (not fallen off)
- Reasonable variation in hours and rates (coefficient of variation < 0.5)

### Current Forecast Calculation

Forecasts are currently calculated as:
- Projected bill amount = median_weekly_hours * latest_bill_rate
- Projected cost amount = median_weekly_hours * latest_cost_rate
- Projected spread amount = projected_bill_amount - projected_cost_amount

## Backtesting Implementation Plan

To properly evaluate the current forecasting model's performance, we'll implement a comprehensive backtesting framework:

1. **Historical Snapshot Approach**
   - Create point-in-time snapshots of historical data (e.g., data as it existed 3 months ago)
   - Apply the current forecasting methodology to these snapshots
   - Compare the resulting forecasts with actual data that occurred after the snapshot date

2. **Backtesting SQL Implementation**
   - Modify the existing forecast SQL to accept a "simulation_date" parameter
   - Filter historical data to only include records before the simulation date
   - Generate forecasts as if we were running the model on that historical date
   - Store results in a new table: `udw.analysis.forecast_backtest_results`

3. **Accuracy Metrics Calculation**
   - Mean Absolute Percentage Error (MAPE) - Primary metric for overall accuracy
   - Mean Absolute Error (MAE) - For understanding absolute magnitude of errors
   - Root Mean Square Error (RMSE) - For penalizing large errors
   - Forecast Bias - To detect systematic over/under-forecasting

4. **Dimensional Analysis**
   - Accuracy by time horizon (1 week out, 2 weeks out, etc.)
   - Accuracy by business segment/client/placement type
   - Accuracy by forecast confidence score
   - Accuracy by historical data stability metrics

## Data Analysis Plan

After establishing baseline performance through backtesting, we'll analyze the data to identify improvement opportunities:

1. **Pattern Analysis**
   - Time series decomposition to identify trend, seasonality, and residual components
   - Autocorrelation analysis to detect cyclical patterns
   - Anomaly detection to identify outliers and special events

2. **Feature Importance Analysis**
   - Correlation analysis between historical metrics and forecast accuracy
   - Identify leading indicators of changes in billing patterns
   - Analyze the impact of business events on forecast accuracy

3. **Segmentation Analysis**
   - Cluster analysis to identify natural groupings of placements with similar patterns
   - Develop segment-specific forecasting approaches
   - Identify segments with consistently higher/lower forecast accuracy

## Model Improvement Strategy

Based on backtesting and analysis results, we'll implement improvements to the three key models:

1. **prep.billing_unified_forecast Improvements**
   - Enhance eligibility criteria based on backtesting results
   - Implement more sophisticated feature engineering
   - Add forecast confidence scoring with proven correlation to accuracy

2. **udw.fact_billing_forecast Improvements**
   - Implement time-horizon specific forecasting approaches
   - Add segment-specific forecasting logic
   - Incorporate seasonality adjustments where relevant
   - Implement confidence intervals for forecasts

3. **udw.fact_billing_unified Improvements**
   - Optimize the integration of actual and forecast data
   - Implement versioning to track forecast changes over time
   - Add metadata to support forecast accuracy analysis



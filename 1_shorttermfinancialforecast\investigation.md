# Financial Forecast Investigation

## Phase 1: Forecast Pipeline Flexibility

### Model Modifications
1. Added configurable `forecast_start_date` parameter to `prep.billing_unified_forecast`
   - Defaults to current_date() if not specified
   - Allows backtesting by setting historical dates

2. Modified historical data filtering
   - Changed from fixed 12-month lookback to dynamic filtering based on forecast_start_date
   - All data before forecast_start_date is now considered historical
   - This enables point-in-time analysis for backtesting

3. Updated eligibility criteria
   - All date-based criteria now use forecast_start_date instead of current_date
   - Maintains consistency in eligibility determination for historical forecasts
   - Preserves existing business logic while enabling backtesting

### Next Steps for Testing
1. Need to validate the changes with different forecast_start_date values:
   ```sql
   -- Test with current date (default behavior)
   dbt run --select prep.billing_unified_forecast
   
   -- Test with historical date (e.g., 3 months ago)
   dbt run --select prep.billing_unified_forecast --vars '{"forecast_start_date": "2023-12-01"}'
   ```

2. Key metrics to validate:
   - Number of eligible placements
   - Distribution of forecast confidence scores
   - Historical data points used for each placement
   - Forecast end dates

3. Potential areas to investigate:
   - Impact on forecast accuracy at different time horizons
   - Effect of historical data availability on eligibility
   - Distribution of forecast confidence scores
   - Seasonal patterns in forecast accuracy

### Questions to Address
1. How does forecast accuracy vary with different forecast_start_date values?
2. Are there specific time periods where the model performs better/worse?
3. What is the optimal amount of historical data needed for accurate forecasts?
4. How do different business segments perform in terms of forecast accuracy?

## Phase 2: Accuracy Validation Framework

### Initial Accuracy Metrics (Q1 2025 Backtest - Overall - Before Simplification)
- **MAE (Mean Absolute Error):** 1257.75
- **RMSE (Root Mean Square Error):** 1933.40
- **MAPE (Mean Absolute Percentage Error):** 474.80%

### Initial Assessment & Next Steps (Before Simplification)
The high MAPE (nearly 500%) indicates significant discrepancies between backtested forecasts and actuals. This suggests that the current forecasting methodology is not accurate enough for the given historical period and warrants further investigation. We need to analyze:
- Are errors concentrated in specific placements or distributed across many?
- What are the largest drivers of error (e.g., specific business factors, outliers, changes in billing patterns)?
- Does the forecast accuracy improve over shorter time horizons (e.g., 1-2 weeks out vs. 3 months out)?

This initial analysis highlights a critical need to refine the forecasting logic rather than just validating the framework. We should re-evaluate the forecast calculation and eligibility criteria based on these results.

### Placement-Level Accuracy Metrics (Top 10 by MAE, Q1 2025 Backtest - Before Simplification)
| PLACEMENT_KEY | MAE       | RMSE        | MAPE         |
|---------------|-----------|-------------|--------------|
| 317039        | 18720.0   | 18720.0     | 100.0%       |
| 318973        | 18600.0   | 18600.0     | 100.0%       |
| 314496        | 17029.90  | 17663.72    | 1853.08%     |
| 317210        | 15561.00  | 15561.00    | 100.0%       |
| 306282        | 14835.67  | 15462.05    | 4039.86%     |
| 311054        | 13263.74  | 14428.80    | 850.97%      |
| 316102        | 12500.0   | 12500.0     | 100.0%       |
| 308012        | 11754.47  | 11758.26    | 545.49%      |
| 319020        | 11440.0   | 11440.0     | 100.0%       |
| 316458        | 11000.0   | 11000.0     | 100.0%       |

### Placement-Level Assessment (Before Simplification)
- The high MAE and RMSE values at the placement level further emphasize the significant inaccuracies of the current forecasting model. 
- The 100% MAPE values for several placements strongly suggest that the actual `SPREAD_AMOUNT` for these placements was zero during the backtesting period, while the forecast had a non-zero value. This is a critical area for investigation as it indicates the model is forecasting revenue for placements that had no actual billing during that time.
- The large variance in MAPE values across different placements (from 100% to over 4000%) indicates that the model's performance is inconsistent and highly dependent on the specific placement.

### Case Study: PLACEMENT_KEY 317039 Analysis
- **Finding:** Only one historical billing record for `PLACEMENT_ID: 317039` found in `DLUKIC_PREP.PREP.BILLING_UNIFIED` for `BILLING_DATE: 2025-02-28`. No data was available in the historical look-back period (July 1, 2024 - December 31, 2024) when the backtest was run with `forecast_start_date` as '2025-01-01'.
- **Impact:** Due to the absence of historical data in the relevant look-back window, `PLACEMENT_KEY: 317039` was not marked as `is_forecast_eligible` in `DLUKIC_PREP.PREP.BILLING_UNIFIED_FORECAST` when running the backtest for Q1 2025. Consequently, no backtest forecast data was generated for this placement in `udw.fact_billing_forecast` and `udw.fact_billing_unified` for Q1 2025.
- **Conclusion:** The 100% MAPE for this placement is a direct result of the forecasting model not generating a forecast for a period where actual billing occurred, because the placement did not have sufficient historical data *within the defined historical window* to meet the eligibility criteria. This highlights that while the eligibility criteria have been relaxed, the underlying data availability for a placement significantly impacts its ability to be forecasted accurately. The model cannot forecast what it has no prior data for.

### Further Analysis of High-Error Eligible Placements (Before Simplification)
- **Finding:** Several placements with high MAE (e.g., 308012, 306282, 314496, 311054) were found to be `IS_FORECAST_ELIGIBLE: true` with substantial `BILLING_RECORD_COUNT` in `DLUKIC_PREP.PREP.BILLING_UNIFIED_FORECAST`. This indicated that for these placements, the issue was not a lack of historical data or ineligibility, but rather the accuracy of the forecast generated by the model despite having sufficient historical patterns.
- **Implication:** The original forecasting methodology (simple extrapolation based on weighted averages of latest, median, and average values) in `udw.fact_billing_forecast.sql` appeared to be insufficient for accurately predicting the `SPREAD_AMOUNT` for these placements.

### Overall Accuracy Metrics (Q1 2025 Backtest - After Simplification)
- **MAE (Mean Absolute Error):** 2148.09
- **RMSE (Root Mean Square Error):** 3274.35
- **MAPE (Mean Absolute Percentage Error):** 993.87%

### Overall Assessment & Next Steps (After Simplification)
- **Finding:** Simplifying the forecast calculation to use only median values resulted in a *significant increase* in all error metrics (MAE, RMSE, and especially MAPE), indicating a substantial degradation in forecast accuracy.
- **Implication:** The original blended average calculation, while still leading to high overall errors, was more effective than relying solely on medians. This suggests that simply tweaking the current linear extrapolation approach by removing components is unlikely to yield significant improvements.
- **Conclusion:** The current forecasting methodology, regardless of the specific averaging technique, is not robust enough for the observed billing patterns. A more fundamental change in approach is needed to improve forecast accuracy.
- **Next Steps:** We should now move towards implementing more sophisticated forecasting approaches as outlined in the `planning.md` document, such as exploring time-horizon specific forecasting, segment-specific forecasting logic, or incorporating seasonality adjustments. This might involve reintroducing and refining the weighted averages or considering entirely new methods.

### Case Study: PLACEMENT_KEY 308012 Historical Pattern Analysis (Q1 2025 Backtest)
- **Finding:** For `PLACEMENT_KEY: 308012`, the backtested `SPREAD_AMOUNT` in Q1 2025 was consistently 22320.0, while the actual `SPREAD_AMOUNT` for the same period fluctuated mostly between 1300 and 2900. 
- **Implication:** This demonstrates a significant and consistent over-forecasting by the current model for this placement. The simple extrapolation methodology is unable to capture the true magnitude and variability of the `SPREAD_AMOUNT` for this placement.
- **Conclusion:** The current forecasting model is not adequately sensitive to the actual billing patterns of placements like 308012, leading to substantial inaccuracies. This further supports the need for more advanced forecasting techniques that can recognize and adapt to historical trends, seasonality, or other underlying patterns in the data.

### Overall Accuracy Metrics (Q1 2025 Backtest - After Revert)
- **MAE (Mean Absolute Error):** 1626.79
- **RMSE (Root Mean Square Error):** 2508.12
- **MAPE (Mean Absolute Percentage Error):** 783.83%

### Overall Assessment & Next Steps (After Revert)
- **Finding:** After reverting the simplification to the forecast calculation, the accuracy metrics did not return to the original "Before Simplification" baseline. While the error metrics are lower than after the simplification, they are still higher than the initial baseline (MAE: 1257.75, RMSE: 1933.40, MAPE: 474.80%). This was observed even after clearing the dbt cache and performing a full refresh.
- **Implication:** This suggests that there might be other factors influencing the accuracy, or an implicit change in the dbt environment or data state. The revert was not perfectly continuous due to external factors or subtle, overlooked changes.
- **Next Steps:** We need to conduct a meticulous review of `prep.billing_unified_forecast.sql` to identify any lingering modifications or discrepancies from its original state. This includes a detailed line-by-line comparison of all filtering, aggregation, and eligibility logic, especially concerning the `forecast_start_date_expr` and `series_type_value` definitions. We also need to consider if there are any changes in the underlying data sources or dbt environment that might be contributing to this persistent shift in accuracy.

### Overall Accuracy Metrics (Q1 2025 Backtest - After Adjusting Blended Weights)
- **MAE (Mean Absolute Error):** 1540.34
- **RMSE (Root Mean Square Error):** 2564.81
- **MAPE (Mean Absolute Percentage Error):** 763.32%

### Overall Assessment & Next Steps (After Adjusting Blended Weights)
- **Finding:** Adjusting the weights in the blended average calculation resulted in a marginal, mixed improvement in accuracy. While MAE and MAPE slightly decreased, RMSE slightly increased, and all metrics remain significantly higher than the original baseline.
- **Implication:** This indicates that further tweaking of the current linear extrapolation approach is unlikely to yield the desired level of accuracy. The model is not sufficiently robust to capture the complexities of the billing patterns.
- **Conclusion:** To achieve substantial improvements in forecast accuracy, we must move beyond simple extrapolation and implement more sophisticated forecasting approaches.
- **Next Steps:** Based on the `planning.md` document, the next logical step is to implement **time-horizon specific forecasting approaches** in `udw.fact_billing_forecast.sql`. This will involve generating forecasts that adapt their methodology or parameters based on how far into the future the forecast is being made.

### Overall Accuracy Metrics (Q1 2025 Backtest - After Implementing Time-Horizon Specific Forecasting)
- **MAE (Mean Absolute Error):** 1608.42
- **RMSE (Root Mean Square Error):** 2657.77
- **MAPE (Mean Absolute Percentage Error):** 785.86%

### Overall Assessment & Next Steps (After Implementing Time-Horizon Specific Forecasting)
- **Finding:** Implementing time-horizon specific forecasting with the chosen weights resulted in a slight *increase* in MAE, RMSE, and MAPE compared to the previous iteration. This indicates that the current set of time-horizon adjustments did not improve (and in some aspects worsened) the overall forecast accuracy.
- **Implication:** While the concept of time-horizon specific forecasting is valuable, the specific weighting scheme applied is not optimal for the observed data patterns. This suggests that the model is still struggling to adapt to the inherent variability and trends in the billing data.
- **Next Steps:** We need to iteratively refine the time-horizon specific weights and potentially explore other parameters or methods within each horizon. This could involve:
    1.  **Refining current weights:** Experiment with different weight distributions for each time horizon (e.g., more aggressive decay or less emphasis on historical averages in later periods).
    2.  **Introducing more sophisticated methods per horizon:** For longer horizons, consider incorporating trend analysis or simple seasonality components if identifiable from the historical data.
    3.  **Analyzing specific placements:** Re-examine the high-error placements (e.g., 308012) to understand if their patterns require even more tailored, time-horizon specific logic.

## Data Quality Checks
To be added after initial testing:
- Outlier analysis
- Data completeness metrics
- Historical pattern analysis
- Forecast accuracy metrics 
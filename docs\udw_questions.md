# General & Modelling

## Surrogate keys
- recommend creating a standard surrogate key pattern
  - general pattern of standard type (key is meaningless, used to join only)
  - easier to implement scd in future (need surrogate keys cause natural key is not enough)
  - solves problem with some dimensions (junk) where there are not natural keys on selected grain
- dbt uses dbt_utils.generate_surrogate_key - mdb5 hash of concat fields


## Modelling dimensions and facts

### Dim or fact or both
General guidelines
- facts are used to calculate measures/metrics kpi
- dimensions are for filtering, group by and rollup

Dim or fact or both ?
- entities with potential to model as both
  - job order
  - placement
  - interview


## Data processing stages

dbt[                                    ]   pbix
    source -> base (view) -> prep -> udw -> pbi (metrics/KPIs)

source.edge.placement
base.placement

prep.placement1
prep.placement_

udw - kimball (dim/facts)

dbt[source -> base -> prep -> ods/mart (semi de-normalized) -> udw] - (pbi)

- Metrics are defined based on udw model


### Incremental processing


- complicates dbt runs and flow
- prep schema contains onlyk new/changed data
 - dont have full facts before udw - need to compute additional data after so udw becomes staging and need additional layer
- sometimes full refresh is needed
- hard to do breaking schema changes
- need logic of what it meas loading a segment of data 
  daily example: loading data for 1/10 - means loading all rows where touched_on is 1/10
  how to deal with related data ? 
- complicates calculated dim attributes (need facts to be merged before calculated derived fields)
  - example: has_submittals flag for job_order (need to have all submittals to calculate)
  - if submittals change need to update dim_job_order - need additional logic etc.


# Specific


## Fiscal period
Fiscal period - could be a set of attributes to group by dates
or separate dimension (needs keys for all related facts)
Do we want unified date or clear separation ?

## JobOrder
Dimension and a fact

## Interview

What is prep_item_id and follow_up_item_id?
Do we need to do some timezone coversions for interview time - EST ?


{"name": "ActivityCreditFact-be113ca9-5110-4062-8363-6e516d68c51d", "mode": "import", "queryGroup": "Facts", "source": {"type": "m", "expression": ["let", "    Source = Snowflake.Databases(SNOW<PERSON>AKE_HOST,SNOWFLAKE_WAREHOUSE,[Role=SNOWFLAKE_ROLE]),", "    Database = Source{[Name=SNOWFLAKE_DATABASE,Kind=\"Database\"]}[Data],", "    Schema = Database{[Name=SNOWFLAKE_SCHEMA,Kind=\"Schema\"]}[Data],", "    Relation = Schema{[Name=\"FACT_ACTIVITY_CREDIT\",Kind=\"Table\"]}[Data],", "    Renamed = Table.RenameColumns(Relation,{", "        {\"ACTIVITY_CREDIT_KEY\", \"ActivityCreditKey\"},", "        {\"EMPLOYEE_KEY\", \"EmployeeK<PERSON>\"},", "        {\"BUSINESS_CENTER_KEY\", \"BusinessCenterKey\"},", "        {\"ACTIVITY_DATE\", \"ActivityDate\"},", "        {\"ACTIVITY_ID\", \"ActivityId\"},", "        {\"ACTIVITY_TYPE\", \"ActivityType\"},", "        {\"ACTIVITY_REPEAT_COUNT\", \"ActivityRepeatCount\"},", "        {\"<PERSON>O<PERSON>_ORDER_KEY\", \"JobOrder<PERSON>ey\"},", "        {\"CONTACT_KEY\", \"ContactKey\"},", "        {\"CLIENT_KEY\", \"<PERSON><PERSON><PERSON><PERSON>\"},", "        {\"CANDIDATE_KEY\", \"Candidate<PERSON><PERSON>\"},", "        {\"PLACEMENT_KEY\", \"PlacementKey\"},", "        {\"SUBMITTAL_KEY\", \"SubmittalKey\"},", "        {\"INTERVIEW_KEY\", \"InterviewKey\"},", "        {\"EMPLOYEE_ROLE_KEY\", \"EmployeeR<PERSON>K<PERSON>\"},", "        {\"BUSINESS_CENTER_ROLE_KEY\", \"BusinessCenterRoleKey\"},", "        {\"CREDIT\", \"Credit\"},", "        {\"CREDIT_ADJUSTED\", \"CreditAdjusted\"}", "    }),", "    PartitionDate = Table.DuplicateColumn(Renamed, \"ActivityDate\", \"PartitionDate\"),", "    PartitionDateType = Table.TransformColumnTypes(PartitionDate,{{\"PartitionDate\", type datetime}}),", "    PartitionDateFilter = Table.SelectRows(PartitionDateType, each [PartitionDate] >= RangeStart and [PartitionDate] < RangeEnd),", "    RemovePartitionDate = Table.RemoveColumns(PartitionDateFilter,{\"PartitionDate\"})", "in", "    RemovePartitionDate"]}}
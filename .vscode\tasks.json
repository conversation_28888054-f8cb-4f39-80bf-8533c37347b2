{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "DBT Run",
            "type": "shell",
            "command": "dbt run",
        },
        {
            "label": "DBT Recompile",
            "type": "shell",
            "command": "dbt clean && dbt compile",
        },
        {
            "label": "DBT Docs Serve",
            "type": "shell",
            "command": "dbt docs generate && dbt docs serve"
        },
        {
            "label": "Print model yml",
            "type": "shell",
            "command": "dbt run-operation generate_model_yaml --args '{\"model_name\": \"${input:model}\"}' | awk \"NR >3 && NF > 0 {print}\"",
            "problemMatcher": []
        },
        {
            "label": "Update model yml file from db",
            "type": "shell",
            "command": "dbt-invoke properties.update --models ${input:model} && sed -i 's/name: .*/\\L&/' $(find ./transform -type f -name \"${input:model}.yml\") ",
            "problemMatcher": []
        },
        {
            "label": "Lowercase names for all model yml files",
            "type": "shell",
            "command": "sed -i 's/name: .*/\\L&/' $(find ./transform -type f -name \"${input:yml_file}\")",
            "problemMatcher": []
        },
        {
            "label": "Docusaurus Build Website",
            "type": "shell",
            "command": "bash /workspace/devops/scripts/build_website.sh",
            "args": [],
            "problemMatcher": []
        },
        {
            "label": "Docusaurus Docs Serve",
            "type": "shell",
            "command": "cd /workspace/website && npm run start",
            "args": [],
            "problemMatcher": []
        },
        {
            "label": "Show PowerBI Params",
            "type": "shell",
            "command": "echo -e \"host: $POWERBI_SNOWFLAKE_HOST_${input:env}\\nwarehouse: $POWERBI_SNOWFLAKE_WAREHOUSE_${input:env}\\ndb: $POWERBI_SNOWFLAKE_DATABASE_${input:env}\\nschema: $POWERBI_SNOWFLAKE_SCHEMA\\nrole: $POWERBI_SNOWFLAKE_ROLE_${input:env}\"",
            "args": [],
            "problemMatcher": []
        }
        
    ],
    "inputs": [
        {
            "id": "model",
            "description": "model:",
            "default": "",
            "type": "promptString"
        },
        {
            "id": "yml_file",
            "description": "yml file:",
            "default": "*.yml",
            "type": "promptString"
        },
        {
            "id": "env",
            "type": "pickString",
            "description": "Which environment ?",
            "options": [
              "DEV",
              "PROD",
            ],
            "default": "DEV"
        }
    ]
}
{{
    config(
        materialized='table'
    )
}}

with placement_check as (
    select
        p.placement_key,
        p.legacy_status,
        p.estimated_end_date,
        max(bc.billing_date) as last_billing_date,
        count(*) as billing_records,
        avg(bc.bill_hours) as avg_hours,
        avg(bc.bill_rate_amount) as avg_bill_rate
    from {{ ref('udw.dim_placement') }} p
    left join {{ ref('udw.fact_billing_unified') }} bc
        on p.placement_key = bc.placement_key
    where bc.billing_date >= dateadd('week', -13, current_date())
    group by 1, 2, 3
)

select
    placement_key,
    legacy_status,
    estimated_end_date,
    last_billing_date,
    billing_records,
    avg_hours,
    avg_bill_rate,
    case
        when last_billing_date >= dateadd('week', -4, current_date()) then 'Pass'
        else 'Fail: No recent billing'
    end as recent_billing_check,
    case
        when estimated_end_date is null then 'Pass: No end date'
        when estimated_end_date > current_date() then 'Pass'
        else 'Fail: End date in past'
    end as end_date_check,
    case
        when avg_hours > 0 then 'Pass'
        else 'Fail: No hours'
    end as hours_check,
    case
        when avg_bill_rate > 0 then 'Pass'
        else 'Fail: No bill rate'
    end as rate_check,
    case
        when last_billing_date >= dateadd('week', -4, current_date())
         and (estimated_end_date is null or estimated_end_date > current_date())
         and avg_hours > 0
         and avg_bill_rate > 0
        then 'Eligible for Forecast'
        else 'Not Eligible'
    end as forecast_eligibility
from placement_check
order by 
    case when forecast_eligibility = 'Eligible for Forecast' then 0 else 1 end,
    last_billing_date desc

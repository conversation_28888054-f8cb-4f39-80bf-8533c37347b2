### 1.1 Update prep.billing_unified_forecast.sql
- [x] Add configurable `forecast_start_date` parameter
- [x] Test parameter defaults to current date when not specified
- [x] Implement logic to generate forecast from any historical start date
- [x] Ensure pattern analysis uses data up to the specified start date only
- [x] Verify existing business logic is preserved
- [ ] Test scope filtering (contracts only, started/set start dates, hourly rates)
- [ ] Confirm no minimum weeks requirement implemented
- [ ] Validate weekly hours and rate calculations (no artificial caps)
- [ ] Ensure business unified grain maintained (split/employee level)
- [ ] Test contract end date projection logic
- [x] Document any changes in code comments

### 1.2 Update udw.fact_billing_forecast.sql
- [x] Use the prep model's configurable start date
- [x] Add `series_type` field with values: 'Forecast', 'Backtest'
- [x] Generate forecasts from the specified start date forward
- [x] Maintain all existing business logic and grain 

### 3.1 Integrate Forecast into fact_billing_unified
- [x] Ensure `series_type` column exists in `fact_billing_unified` schema with values: 'Actual', 'Forecast', 'Backtest'
- [x] Update model to tag actual data as `series_type = 'Actual'`
- [x] Integrate forecast data for future dates (from `fact_billing_forecast`)
- [x] Include backtest data for historical dates (from `fact_billing_forecast`)
- [x] Verify how `fact_billing_forecast` data flows into `fact_billing_unified`
- [ ] Test data integrity and grain consistency
- [ ] Validate no duplication between series types
- [ ] Ensure dimensional attributes preserved
- [ ] Test incremental load behavior with new series types
- [ ] Update model documentation 

### 2.2 Develop Accuracy Metrics
- [x] Build weekly accuracy calculation: `(forecast - actual) / actual * 100`
- [x] Create placement-level accuracy aggregation
- [x] Calculate overall model accuracy baseline
- [ ] Build ranking query for placements by accuracy
- [ ] Create accuracy distribution analysis
- [x] Document baseline metrics for future comparison
- [ ] Validate accuracy calculation logic with sample data

### 2.3 Accuracy Analysis & Insights
- [x] Run accuracy metrics on full backtest dataset
- [x] Identify systematic biases or patterns
- [ ] Document which placement types forecast well/poorly
- [x] Analyze if errors are concentrated or distributed
- [x] Record insights in `investigation.md`
- [x] Determine if core logic needs adjustment 
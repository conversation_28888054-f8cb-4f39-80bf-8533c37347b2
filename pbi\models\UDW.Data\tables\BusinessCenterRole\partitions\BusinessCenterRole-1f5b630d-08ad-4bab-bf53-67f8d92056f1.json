{"name": "BusinessCenterRole-1f5b630d-08ad-4bab-bf53-67f8d92056f1", "mode": "import", "queryGroup": "Dimensions", "source": {"type": "m", "expression": ["let", "    Source = Snowflake.Databases(SNOW<PERSON>AKE_HOST,SNOWFLAKE_WAREHOUSE,[Role=SNOWFLAKE_ROLE]),", "    Database = Source{[Name=SNOWFLAKE_DATABASE,Kind=\"Database\"]}[Data],", "    Schema = Database{[Name=SNOWFLAKE_SCHEMA,Kind=\"Schema\"]}[Data],", "    Relation = Schema{[Name=\"DIM_BUSINESS_CENTER_ROLE\",Kind=\"Table\"]}[Data],", "    Renamed = Table.RenameColumns(Relation,{", "        {\"BUSINESS_CENTER_ROLE_KEY\", \"BusinessCenterRoleKey\"},", "        {\"SOURCE\", \"Source\"},", "        {\"NAME\", \"Name\"},", "        {\"DESCRIPTION\", \"Description\"}})", "in", "    <PERSON>amed"]}}
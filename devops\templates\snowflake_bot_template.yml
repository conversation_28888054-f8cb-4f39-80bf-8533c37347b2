parameters:
- name: specFile
  type: string
  default: ''
- name: actions
  type: string
  default: 'CREATE_ALL'
  values:
  - CREATE_DATABASES
  - CREATE_ROLES
  - CREATE_WAREHOUSES
  - CREATE_ALL

steps:
- script: |
    echo spec=${{parameters.specFile}}
    echo user=$SNOWFLAKE_BOT_USER
    python -m utils.snowflake_bot --file ${{parameters.specFile}} --actions ${{parameters.actions}}
  displayName: 'Run snowflake bot'
  env:
    SNOWFLAKE_BOT_PASSWORD: $(SNOWFLAKE_BOT_PASSWORD)
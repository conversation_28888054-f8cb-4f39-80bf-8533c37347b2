// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.194.3/containers/python-3
{
    "name": "Judge UDW",
    "workspaceFolder": "/workspace",
    "dockerComposeFile": "docker-compose.yml",
    "service": "dev",
    // Set *default* container specific settings.json values on container create.
    "customizations": {
        // Configure properties specific to VS Code.
        "vscode": {
            // Set *default* container specific settings.json values on container create.
            "settings": {
                "python.defaultInterpreterPath": "/usr/local/bin/python",
                "python.formatting.autopep8Path": "/usr/local/py-utils/bin/autopep8",
                "python.formatting.blackPath": "/usr/local/py-utils/bin/black",
                "python.formatting.yapfPath": "/usr/local/py-utils/bin/yapf",
                "python.testing.pytestPath": "/usr/local/py-utils/bin/pytest"
            },
            // Add the IDs of extensions you want installed when the container is created.
            "extensions": [
                "ms-python.python",
                "ms-python.vscode-pylance",
                "PKief.material-icon-theme", // matrial icons
                "mutantdino.resourcemonitor", // cpu & memory monitor
                "mikestead.dotenv", // .env file syntax
                "spmeesseman.vscode-taskexplorer", // task explorer gui
                "gruntfuggly.todo-tree", // todo highlights and tree
                "bastienboutonnet.vscode-dbt", // snippets for jinja and dbt
                "henriblancke.vscode-dbt-formatter", // jinja dbt formatter
                "innoverio.vscode-dbt-power-user", // dbt power user 
                "dorzey.vscode-sqlfluff", //sqlfluff linting and formatter
                "samuelcolvin.jinjahtml", // Syntax highlighting for jinja
                "redhat.vscode-yaml" // YAML Language support
            ]
        }
    },
    // Use 'forwardPorts' to make a list of ports inside the container available locally.
    // "forwardPorts": [],
    // Use 'postCreateCommand' to run commands after the container is created.
    "postCreateCommand": "/workspace/.devcontainer/dev_init.sh",
    // Comment out connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
    "remoteUser": "vscode"
}
# UDW

UDW represents a unified data warehouse built upon source Judge operational systems, which consolidates and related all information relevant to reporting and analytics. 

## Technologies

*Snowflake* - Database system
*Alteryx* - Extract & Loading
*dbt* - data transformation 


## Environments

In order to best separate data assets and prevent potential cross environment impact and/or leakege a separate snowflake account should be setup for each environment


| Environment | Description | Details| Snowflake |
| ----------- | ----------- | ----------- | ----------- |
| DEV | Development |For developers/dbas only<br>Develop and test changes<br>Isolated from anyone else<br>Playground<br>Able to bolt on tool on final udw (change connection string|DEV|
| CI | Development ||DEV?|
| UAT | User Acceptance Testing |Used by end users and analysts<br>For testing and validation if the end product gives the end-user the usability required<br> Testing significant changes before full production deployment|UAT|
| PROD | Production | Production environment with up to date data|PROD|



## Roles


| Role        | Description | Priviliges  | Inherited from |
| ----------- | ----------- | ----------- |-----------     |
| SYSADMIN    | System administrator responsible for setting up snowflake<br> Responsible for creating all objects such as roles,users, warehouses etc. | Global administration ||
| DBA         | Database administrator.<br> Owner of databases and its constituents | Database administration<br>Can assign roles<br/>Can something   |
| LOADER      | Used by service accounts for data loading tools | Write access to source databases only | |
| TRANSFORMER | Used by developer and transformation tools such as dbt that need | Read access to sources.<br> Full access to all other databases | DBA |
| ANALYST     | Used by QA, analysts <br> Focus is on querying and interacting with data.| Read only access ||
| REPORTER    | Useb by service accounts for BI tools (PowerBi, Tableau) |  | ANALYST |


## Sources

Every sources represents and entity,event or a single data asset.
Every source is loaded into a single table organized into schemas based on originating system.

### Organization

- Single databases for all sources. 
- Sources are loaded by an ELT tool such as Alteryx or Fivetran connected using the Loader role.
- Loading of sources is independent of transformations
- Sources can be loaded incrementally or full refresh

| Schema      | Description | Data characteristics|
| ----------- | ----------- | ------------------- |
| EDGE        | EDGE ATS - core system  | Core, Recruiting, JobOrders, Activities |
| FILEMAKER   |             ||
| GREAT_PLAIN | ERP & Financial system ||
| TAP         |  ||
| UNIT4       |  ||

## Warehouses

Separate warehouses based on pattern of use in order to reduce strain and distruption on processes and users and ensure optimal performance.

| Warehouse    | Description | Priviliges  | Inherited from |
| -----------  | ----------- | ----------- |-----------     |
| LOADING      | For loading data into source using tools such as (alteryx,Fivetran)|||
| TRANSFORMING | Performs all transformations. Only used by dbt |||
| REPORTING    | For use by BI and visualization tools to run analytical queries and reports |||
| *DEVELOPMENT*| For use by developers during dev cycles. If we want to track usage and credits separate |||



## Databases

| Database    | Description | Data characteristics| Internal organization| Materialization |
| ----------- | ----------- | ----------- | ----------- |----------- |
| JUDGE_SOURCE      | Data landing layer.<br> Data is populated by loaders | Raw data| Schema by source system | views |
| {ENV_}PREP   | Data integration layer.<br>Intermediate data staging and transformation | Intermediate and transformed data | Schema by data area, purpose | tables & views|
| {ENV_}UDW  | Unified data warehouse. | Unified conformed data master data | Data in tables - Schema by data area<br>access layer via views | data - tables <br>exposures - views|
| {ENV_}PBI  | Facts and dimensions for reporting | UDW data spit into facts and dimensions for PBI consumption | Data in tables - Schema by data area<br>access layer via views | data - tables <br>exposures - views|


### Schemas


| Schema    | Description |
| ----------- | ----------- |
| SOURCE.EDGE      | Source data from EDGE |
| SOURCE.TAP      | Source data from TAP |
| SOURCE.GRAT_PLAINS      | Source data from Great Plains |
| SOURCE.UNIT4      | Source data from Unit4 |


## Naming convention

singular
dimension prefix: dim_{name}
fact prefix: fact_{name}
dimension keys: dimension_key


## DBT Namespaces & Layers

| Namespace  | Description | Database.Schema | Materialization | Characteristic |
| ---------- | ------------| --------------- | --------------- | -------------- |
| **Source** | All registered sources            | JUDGE_SOURCE.*  | Tables          | Loaded by loaders (tools, processes etc.)<br>Contains all/latest available data<br>Can be checked for freshness<br>Single source per table/entity |
| **Ingest** | Intake layer for all data into DW | {CONTEXT}_PREP.PREP     | Views           | Projection on top of sources<br>Generated data or seed data<br>Light data typing, renames, skip columns (fix it once)<br>No heavy transforms or joins<br>Single dbt model per source|
| **PREP**   | Intermediate layer to stage data  | {CONTEXT}_PREP.PREP    | Various         | Data staging, temporary no user access<br> Cupute once but only needed temporarily |
| **ODS**    | Operational Data Store            | {CONTEXT}_PREP.ODS      | Tables/Various  |Retain original grain & information (low-level atomic data)<br>Data reflects closer to original sources (names , grain, etc)<br> Light denormalization (lookups etc)<br>Possible internal reporting layer|
| **UDW**    | Unified Data Warehouse            | {CONTEXT}_UDW.UDW       | Tables| Dimensional model with start schema<br> Can aggregate for performance/simplicity/convinience<br> Light transforms over ODS (denormalization ,pre-joins) <br> Every model must represents a dimension or a fact<br> Only conformed dimensions (no role playing) <br> Main analytics & reporting source for BI, analysts, tools etc. |


### PowerBI
PowerBI semantic model layer
- exposes UDW data via PowerBI ecosystem to end users
- Direct query model (default)
- Relationships and filtering context
- Light transformation (add, hide fields)
- Role playing dimenions
  - primary business center
  - emplyee roles (recruiter, sales rep, manager)
  - dates (job order date, activity date, billing date)
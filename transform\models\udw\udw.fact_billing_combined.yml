version: 2
models:
  - name: udw.fact_billing_combined
    description: "Combined view of actual and forecasted billing data"
    config:
      alias: fact_billing_combined
    columns:
      - name: fact_key
        description: "Unique identifier for each record"
        tests:
          - unique
          - not_null
      - name: placement_key
        description: "Foreign key to placement dimension"
        tests:
          - not_null
          - relationships:
              to: ref('udw.dim_placement')
              field: placement_key
      - name: date
        description: "Date of the billing/forecast record"
        tests:
          - not_null
      - name: series_indicator
        description: "Indicates if the record is actual or forecasted data"
        tests:
          - accepted_values:
              values: ['Actual', 'Forecast']
      - name: hours
        description: "Actual or forecasted hours"
      - name: bill_rate
        description: "Billing rate"
      - name: cost_rate
        description: "Cost rate"
      - name: bill_amount
        description: "Total billing amount"
      - name: cost_amount
        description: "Total cost amount"
      - name: spread_amount
        description: "Spread (profit) amount"
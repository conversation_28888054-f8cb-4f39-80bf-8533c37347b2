import os
import yaml
from metadata.workflow.metadata import MetadataWorkflow
from metadata.ingestion.api.parser import parse_workflow_config_gracefully
# python devops/openmetadata_workflow.py

def run():
    with open("openmetadata_config.yaml") as file:
        workflow_config = yaml.safe_load(file)

    config = parse_workflow_config_gracefully(workflow_config)
    workflow = MetadataWorkflow(config)
    workflow.execute()
    workflow.raise_from_status()
    workflow.print_status()
    workflow.stop()


if __name__ == "__main__":
    run()


# import requests
# headers= {"Authorization": "Bearer eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************.vV7pJMtV166_llft1_QTYaQhQeCKUTHxHJ94nfCKhdy0RwfgZV600K89oPNa5erHOWIkXtpEG5AX28wAkrZq3HEmBMtNC2dyl2RnNfKzF-NXKv2FM7xSsH1SxFcozkhpuaEAMtM4tpLNeaSqn6rOgXR2HZEbvG5Y2qci3j-8SCTk2Z2rxqA6Q7GwRRZljVCnX_fRUYlqdGYrfHOkxhGc6e3FgLf05uraoG4_Ydx2j1iT7B26-Mqnu4gk4KXTdOKrbbKhj0LlmYH9PcMS7FxrpaZasrxNJHoVypNt8C1UhxlZDNhQJBgJHf-40k34hnM6l8tXaW8nhjThejMerIF56A"}
# resp = requests.get("http://10.4.213.8:8585/api/v1/databases", headers=headers)
# resp.json()
# resp = requests.get("http://10.4.213.8:8586/healthcheck?pretty=true")



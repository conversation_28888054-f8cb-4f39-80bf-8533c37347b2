-- unified billing (perm+contract) facts with forecast extension
with
billing as (
    select *,
           'Actual' as series_indicator  -- Add this field to the billing CTE
    from {{ ref('prep.billing_unified') }}
),
employee as (
    select *
    from {{ ref('udw.dim_employee') }}
),
employee_tenure as (
    select *
    from {{ ref('udw.dim_employee_tenure') }}
),
-- Add forecast data
forecast as (
    select
        forecast_key as billing_unified_id,
        'C' as line_of_business_id,
        placement_key,
        job_order_id,
        client_id,
        candidate_id,
        null as sales_lead_id,
        employee_id,
        business_center_id,
        employee_role_id,
        null as billing_info_id,
        forecast_date,
        hours as bill_hours,
        0 as nobill_hours,
        0 as adjust_hours,
        bill_amount as gross_amount,
        bill_amount,
        cost_amount,
        spread_amount,
        bill_rate as bill_rate_amount,
        cost_rate as cost_rate_amount,
        series_indicator
    from {{ ref('udw.fact_billing_forecast') }}
),
-- Combine actual and forecast data
combined_billing_pre_id as (
    select
        MD5(CONCAT_WS('||',
            billing.line_of_business_id,
            billing.placement_id,
            billing.job_order_id,
            billing.client_id,
            billing.candidate_id,
            billing.sales_lead_id,
            billing.employee_id,
            billing.business_center_id,
            billing.billing_date,
            billing.bill_hours,
            billing.nobill_hours,
            billing.adjust_hours,
            billing.gross_amount,
            billing.bill_amount,
            billing.cost_amount,
            billing.spread_amount,
            billing.bill_rate_amount,
            billing.cost_rate_amount,
            billing.billing_role
        )) as source_billing_id, -- Use a temporary ID for the union
        coalesce(billing.line_of_business_id, 'U') as line_of_business_id,
        coalesce(billing.placement_id, 0) as placement_id,
        coalesce(billing.job_order_id, 0) as job_order_id,
        coalesce(billing.client_id, 0) as client_id,
        coalesce(billing.candidate_id, 0) as candidate_id,
        coalesce(billing.sales_lead_id, 0) as sales_lead_id,
        coalesce(billing.employee_id, '') as employee_id,
        coalesce(billing.business_center_id, 0) as business_center_id,
        billing.billing_role,
        null as employee_role_id,
        null as billing_info_id,
        billing.billing_date,
        billing.pay_code,
        billing.bill_hours,
        billing.nobill_hours,
        billing.adjust_hours,
        billing.gross_amount,
        billing.bill_amount,
        billing.cost_amount,
        billing.spread_amount,
        billing.bill_rate_amount,
        billing.cost_rate_amount,
        billing.series_indicator
    from billing
    
    union all
    
    -- Only include forecast data for the current week and beyond, or all backtest data
    select
        forecast.billing_unified_id as source_billing_id, -- Use forecast_key as source_id
        coalesce(forecast.line_of_business_id, 'U') as line_of_business_id,
        coalesce(forecast.placement_key, 0) as placement_id,
        coalesce(forecast.job_order_id, 0) as job_order_id,
        coalesce(forecast.client_id, 0) as client_id,
        coalesce(forecast.candidate_id, 0) as candidate_id,
        coalesce(forecast.sales_lead_id, 0) as sales_lead_id,
        coalesce(forecast.employee_id, '') as employee_id,
        coalesce(forecast.business_center_id, 0) as business_center_id,
        'Contract' as billing_role,
        null as employee_role_id,
        null as billing_info_id,
        forecast.forecast_date as billing_date,
        'CONTRACT' as pay_code,
        forecast.bill_hours,
        forecast.nobill_hours,
        forecast.adjust_hours,
        forecast.gross_amount,
        forecast.bill_amount,
        forecast.cost_amount,
        forecast.spread_amount,
        forecast.bill_rate_amount,
        forecast.cost_rate_amount,
        forecast.series_indicator
    from forecast
    where forecast.series_indicator = 'Backtest'
       or (forecast.series_indicator = 'Forecast' and forecast.forecast_date >= date_trunc('week', current_date()))
),
combined_billing as (
    select
        row_number() over (order by (select 0)) as billing_unified_id, -- Generate a unique numeric ID after union
        line_of_business_id,
        placement_id,
        job_order_id,
        client_id,
        candidate_id,
        sales_lead_id,
        employee_id,
        business_center_id,
        billing_role,
        employee_role_id,
        billing_info_id,
        billing_date,
        pay_code,
        bill_hours,
        nobill_hours,
        adjust_hours,
        gross_amount,
        bill_amount,
        cost_amount,
        spread_amount,
        bill_rate_amount,
        cost_rate_amount,
        series_indicator
    from combined_billing_pre_id
),
final as (
    select
        combined_billing.billing_unified_id as billing_unified_key,
        coalesce(combined_billing.line_of_business_id, 'U') as line_of_business_key,
        coalesce(combined_billing.placement_id, 0) as placement_key,
        coalesce(combined_billing.job_order_id, 0) as job_order_key,
        coalesce(combined_billing.client_id, 0) as client_key,
        coalesce(combined_billing.candidate_id, 0) as candidate_key,
        coalesce(combined_billing.sales_lead_id, 0) as sales_lead_key,
        coalesce(employee.employee_key, 0) as employee_key,
        coalesce(combined_billing.business_center_id, 0) as business_center_key,
        4 as business_center_role_key,
        combined_billing.billing_role,
        coalesce(combined_billing.employee_role_id, 0) as employee_role_key,
        coalesce(employee_tenure.employee_tenure_key, 0) as employee_tenure_key,
        coalesce(combined_billing.billing_info_id, 0) as billing_info_key,
        combined_billing.billing_date,
        combined_billing.pay_code,
        combined_billing.bill_hours,
        combined_billing.nobill_hours,
        combined_billing.adjust_hours,
        combined_billing.gross_amount,
        combined_billing.bill_amount,
        combined_billing.cost_amount,
        combined_billing.spread_amount,
        combined_billing.bill_rate_amount,
        combined_billing.cost_rate_amount,
        combined_billing.series_indicator
    from combined_billing
    left join employee
    on employee.employee_id = combined_billing.employee_id
    left join employee_tenure
    on employee.employee_key = employee_tenure.employee_key
        and combined_billing.billing_date between employee_tenure.start_date and employee_tenure.end_date
)
select
    billing_unified_key,
    line_of_business_key,
    placement_key,
    job_order_key,
    client_key,
    candidate_key,
    sales_lead_key,
    employee_key,
    business_center_key,
    business_center_role_key,
    employee_role_key,
    employee_tenure_key,
    billing_info_key,
    billing_date,
    pay_code,
    bill_hours,
    nobill_hours,
    adjust_hours,
    gross_amount,
    bill_amount,
    cost_amount,
    spread_amount,
    bill_rate_amount,
    cost_rate_amount,
    series_indicator
from final







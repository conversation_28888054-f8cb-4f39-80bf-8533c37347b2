# snowflake bot to automate creation of objects based on yml spec
parameters:
- name: variableGroup
  displayName: Target Variable Group
  type: string
  
variables:
- group: ${{parameters.variableGroup}} # variable group
- name: specFile
  value: $(SNOWFLAKE_BOT_SPEC_FILE)

steps:
- template: templates/python_setup_template.yml
- template: templates/snowflake_bot_template.yml
  parameters:
    specFile: ${{ variables.specFile }}
    actions: CREATE_ALL
- template: templates/permifrost_bot_template.yml
  parameters:
    specFile: ${{ variables.specFile }}
    action: run
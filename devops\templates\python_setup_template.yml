# setup python and pip dependencies

steps:
- task: UsePythonVersion@0
  displayName: 'Use Python 3.9'
  inputs:
    versionSpec: '3.9'
    addToPath: true
    architecture: 'x64'

- script: |
    python -m pip install --upgrade pip
    pip install -r requirements.txt
  displayName: 'Install python dependencies'

- script: |
    echo "PWD is $(pwd)"
    echo "PYTHONPATH is $PYTHONPATH"
  displayName: 'Setup PYTHONPATH'
  env:
    PYTHONPATH: '$(System.DefaultWorkingDirectory);$(PYTHONPATH)'
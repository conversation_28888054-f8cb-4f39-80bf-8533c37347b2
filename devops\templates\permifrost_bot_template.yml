# snowflake bot to automate creation of objects based on yml spec
parameters:
- name: specFile
  type: string
  default: ''
- name: action
  type: string
  default: 'spec-test'
  values:
  - run
  - spec-test

steps:

- script: |
    echo ${{parameters.specFile}}
  displayName: 'Echo spec file'
- script: |
    permifrost ${{parameters.action}} ${{parameters.specFile}}
  displayName: 'Sync permission grants via permifrost'
  env:
    PERMISSION_BOT_ACCOUNT: $(SNOWFLAKE_ACCOUNT)
    PERMISSION_BOT_USER: $(SNOWFLAKE_BOT_USER)
    PERMISSION_BOT_PASSWORD: $(SNOWFLAKE_BOT_PASSWORD)
    PERMISSION_BOT_WAREHOUSE: $(SNOWFLAKE_ADMIN_WAREHOUSE)
    PERMISSION_BOT_DATABASE: $(SNOWFLAKE_ADMIN_DATABASE)
    PERMISSION_BOT_ROLE: $(SNOWFLAKE_BOT_DEFAULT_ROLE)
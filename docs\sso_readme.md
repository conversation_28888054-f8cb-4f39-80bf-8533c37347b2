## Snowflake SSO


## Configure and test Azure AD SSO for Snowflake
Configure and test Azure AD SSO with Snowflake using a test user called <PERSON><PERSON><PERSON>. For SSO to work, you need to establish a link relationship between an Azure AD user and the related user in Snowflake.

To configure and test Azure AD SSO with <PERSON><PERSON>lake, perform the following steps:

1. Configure Azure AD SSO[https://docs.microsoft.com/en-us/azure/active-directory/saas-apps/snowflake-tutorial#configure-azure-ad-sso] - to enable your users to use this feature.
    1. Create an Azure AD test user - to test Azure AD single sign-on with B.Simon.
    2. Assign the Azure AD test user - to enable <PERSON><PERSON> to use Azure AD single sign-on.
2. Configure Snowflake SSO - to configure the single sign-on settings on application side.
3. Create Snowflake test user - to have a counterpart of <PERSON><PERSON>Simon in Snowflake that is linked to the Azure AD representation of user.
4. Test SSO - to verify whether the configuration works.


## Resources

Configure Snowflake for automatic user provisioning
https://docs.microsoft.com/en-us/azure/active-directory/saas-apps/snowflake-provisioning-tutorial

Azure AD SSO integration with Snowflake
https://docs.microsoft.com/en-us/azure/active-directory/saas-apps/snowflake-tutorial

Managing Users by Integrating Snowflake with Azure Active Directory
https://www.youtube.com/watch?v=mGVjDZWwls4


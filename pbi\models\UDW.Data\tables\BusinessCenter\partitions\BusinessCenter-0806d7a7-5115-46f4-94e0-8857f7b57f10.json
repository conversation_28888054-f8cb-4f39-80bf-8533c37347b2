{"name": "BusinessCenter-0806d7a7-5115-46f4-94e0-8857f7b57f10", "mode": "import", "queryGroup": "Dimensions", "source": {"type": "m", "expression": ["let", "    Source = Snowflake.Databases(SNOW<PERSON>AKE_HOST,SNOWFLAKE_WAREHOUSE,[Role=SNOWFLAKE_ROLE]),", "    Database = Source{[Name=SNOWFLAKE_DATABASE,Kind=\"Database\"]}[Data],", "    Schema = Database{[Name=SNOWFLAKE_SCHEMA,Kind=\"Schema\"]}[Data],", "    Relation = Schema{[Name=\"DIM_BUSINESS_CENTER\",Kind=\"Table\"]}[Data],", "    Renamed = Table.RenameColumns(Relation,{", "        {\"BUSINESS_CENTER_KEY\", \"BusinessCenterKey\"},{\"NAME\", \"Name\"},{\"NAME_ALT\", \"NameAlt\"},{\"LOCATION_NAME\", \"LocationName\"},{\"DIVISION_NAME\", \"DivisionName\"},{\"CORP_NAME\", \"CorpName\"},{\"INDUSTRY\", \"Industry\"},", "        {\"GP_LOCATION_SHORT_NAME\",\"GP.LocationShortName\"},{\"GP_LOCATION\",\"GP.Location\"},{\"CIR_UNIT_NAME\",\"CIR.UnitName\"},{\"CIR_UNIT_ABBREVIATION\",\"CIR.UnitAbbreviation\"},{\"CIR_UNIT_SHORTNAME\",\"CIR.UnitShortName\"}, {\"CIR_UNIT_LONGNAME\",\"CIR.UnitLongName\"}", "    })", "in", "    <PERSON>amed"]}}
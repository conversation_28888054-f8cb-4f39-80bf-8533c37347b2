{{
    config(
        materialized='table'
    )
}}

with forecast_validation as (
    select 
        f.placement_key,
        p.legacy_status,
        p.estimated_end_date,
        min(f.billing_date) as first_forecast,
        max(f.billing_date) as last_forecast,
        avg(f.bill_hours) as avg_hours,
        avg(f.bill_rate_amount) as avg_bill,
        count(*) as forecast_weeks,
        sum(f.bill_amount) as total_projected_bill,
        sum(f.spread_amount) as total_projected_spread
    from {{ ref('udw.fact_billing_unified') }} f
    inner join {{ ref('udw.dim_placement') }} p 
        on f.placement_key = p.placement_key
    where f.series_indicator = 'Forecast'
    group by 1,2,3
)
select 
    *,
    case 
        when last_forecast > estimated_end_date then 'WARNING: Forecast extends beyond end date'
        when forecast_weeks > 12 then 'WARNING: More than 12 weeks forecast'
        when avg_hours = 0 then 'WARNING: Zero hours'
        else 'OK'
    end as validation_status
from forecast_validation
order by validation_status desc



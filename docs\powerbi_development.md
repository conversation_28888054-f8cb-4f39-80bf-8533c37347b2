# Power BI

## Purpose

This is a working document meant to guide development and release/publishing process for PowerBI reporting within the UDW project.

This document assumes familiary with PBI and associated concepts, and outlines the process of working with the building blocks. For background on PBI in general, please refer here. 


## Development

### Development Work In Scope
The focus of this document is reporting work within our project development environment. It is possible to create reports from scratch just by directly working with the dataset, or modify an existing publishing report: but, this work is outside of project version control and is strictly individual and individually supported.  

### Getting Started
For help working with git/version control, please see here. 

1. Get the latest version of the project. Either, (a) clone the project repo, or (b) if you already have the repo cloned sync-up to make sure you have the latest version.

2. Hydrate the PBIT files with your credentails (dev or prod, whichever is more appropriate)

You should now have an editable report (or dataset) in PBI Desktop that you can work with.

### Commiting Work To Repo

(This process is being defined)



## Publish

(This process is being defined)
judge_udw: # this needs to match the profile: in your dbt_project.yml file
  target: dev
  outputs:
    dev:
      type: snowflake
      account: "{{ env_var('SNOWFLAKE_ACCOUNT') }}"
      user: "{{ env_var('SNOWFLAKE_USER') }}"
      password: "{{ env_var('SNOWFLAKE_PASSWORD') }}"
      database: "{{ env_var('SNOWFLAKE_TRANSFORM_DATABASE') }}"
      role: "{{ env_var('SNOWFLAKE_TRANSFORM_ROLE') }}"
      warehouse: "{{ env_var('SNOWFLAKE_TRANSFORM_WAREHOUSE') }}"
      schema: "{{ env_var('SNOWFLAKE_TRANSFORM_SCHEMA') }}"
      query_tag: "{{ env_var('DBT_QUERY_TAG') }}"
      threads: "{{ env_var('DBT_THREADS') | as_number }}"
      client_session_keep_alive: False
      authenticator: username_password_mfa

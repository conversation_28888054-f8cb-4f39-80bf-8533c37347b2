-- Analyze forecast quality and coverage
with actual_metrics as (
  select
    count(distinct placement_key) as active_placements,
    count(distinct employee_key) as active_employees,
    sum(bill_amount) as total_bill_amount,
    sum(spread_amount) as total_spread_amount
  from {{ ref('udw.fact_billing_unified') }}
  where billing_date >= dateadd('month', -3, current_date())
    and billing_date < current_date()
    and series_indicator = 'Actual'
),

forecast_metrics as (
  select
    count(distinct placement_key) as forecasted_placements,
    count(distinct employee_key) as forecasted_employees,
    count(*) as forecast_records,
    min(billing_date) as min_forecast_date,
    max(billing_date) as max_forecast_date,
    avg(bill_hours) as avg_hours,
    avg(bill_rate_amount) as avg_bill_rate,
    avg(bill_amount) as avg_bill_amount,
    sum(bill_amount) as forecast_total_bill,
    sum(spread_amount) as forecast_total_spread
  from {{ ref('udw.fact_billing_unified') }}
  where series_indicator = 'Forecast'
)

select
  a.active_placements,
  a.active_employees,
  a.active_placements as eligible_placements, -- Simplified for now
  f.forecasted_placements,
  f.forecasted_employees,
  f.forecast_records,
  f.min_forecast_date,
  f.max_forecast_date,
  f.avg_hours,
  f.avg_bill_rate,
  f.avg_bill_amount,
  f.forecast_total_bill,
  f.forecast_total_spread,
  (f.forecasted_placements * 100.0 / a.active_placements) as pct_eligible_forecasted,
  (f.forecast_total_bill / a.total_bill_amount) as forecast_to_actual_ratio
from actual_metrics a
cross join forecast_metrics f

